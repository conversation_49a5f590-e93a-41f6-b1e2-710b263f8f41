// /* models */
const InventoryHistory = require('../models/inventory-history.model');
const EquipmentOrderHistory = require('../models/equipment-order-history.model');
const PmManageOrderEquipment = require('../models/pm-order-manage-equipment.model');
const PmOrder = require('../models/pm-order.model');
const EquipmentOrder = require('../models/equipment-order.model');
const ReturnOrderHistory = require('../models/return-order-history.model');
const ReturnOrder = require('../models/return-order.model');
const ShoppingCart = require('../models/shopping-cart.model');
const Equipment = require('../models/equipment.model');

/**
 * Clear order data
 */
exports.up = async () => {
  try {
    const orderedEquipment = await InventoryHistory.aggregate([
      {
        $match: {
          deletedAt: null,
          type: { $ne: 'purchase' },
        },
      },
      {
        $group: {
          _id: '$equipment',
          statuses: { $addToSet: '$status' },
        },
      },
      {
        $project: {
          equipment: '$_id',
          hasLinked: { $in: ['linked', '$statuses'] },
          hasInStock: { $in: ['in-stock', '$statuses'] },
        },
      },
      {
        $facet: {
          returnedToWarehouse: [
            {
              $match: {
                hasLinked: true,
                hasInStock: true,
              },
            },
            {
              $lookup: {
                from: 'inventory-histories',
                let: { eqId: '$equipment' },
                pipeline: [
                  {
                    $match: {
                      $expr: {
                        $and: [
                          { $eq: ['$equipment', '$$eqId'] },
                          { $eq: ['$status', 'in-stock'] },
                          { $ne: ['$type', 'purchase'] },
                          { $eq: ['$deletedAt', null] },
                        ],
                      },
                    },
                  },
                ],
                as: 'inStockEntries',
              },
            },
            {
              $project: {
                equipment: 1,
                quantity: { $sum: '$inStockEntries.quantity' },
              },
            },
          ],
          stillInField: [
            {
              $match: {
                hasLinked: true,
                hasInStock: false,
              },
            },
            {
              $lookup: {
                from: 'inventory-histories',
                let: { eqId: '$equipment' },
                pipeline: [
                  {
                    $match: {
                      $expr: {
                        $and: [
                          { $eq: ['$equipment', '$$eqId'] },
                          { $eq: ['$status', 'linked'] },
                          { $ne: ['$type', 'purchase'] },
                          { $eq: ['$deletedAt', null] },
                        ],
                      },
                    },
                  },
                ],
                as: 'linkedEntries',
              },
            },
            {
              $project: {
                equipment: 1,
                quantity: { $sum: '$linkedEntries.quantity' },
              },
            },
          ],
        },
      },
    ]);

    for (const item of orderedEquipment[0].stillInField) {
      await Equipment.findOneAndUpdate(
        { _id: item.equipment },
        { $inc: { quantity: item.quantity } },
        { upsert: true }
      );
    }

    // eslint-disable-next-line no-undef
    await Promise.all([
      EquipmentOrder.deleteMany({}),
      PmOrder.deleteMany({}),
      PmManageOrderEquipment.deleteMany({}),
      ShoppingCart.deleteMany({}),
      EquipmentOrderHistory.deleteMany({}),
      ReturnOrder.deleteMany({}),
      ReturnOrderHistory.deleteMany({}),
      InventoryHistory.deleteMany({ type: { $ne: 'purchase' } }),
    ]);

    console.log('Data aggregation and cleanup completed successfully.');
  } catch (error) {
    console.error('Error during data cleanup:', error);
    throw error;
  }
};
