const notificationService = require('../services/notification.service');
const { successResponse, errorResponse } = require('../utils/response.utils');
const constantUtils = require('../utils/constants.utils');
const HTTP_STATUS = require('../utils/status-codes');

/**
 * Get all notification
 *
 * @param {Object} req
 * @param {Object} res
 */
exports.getAllNotification = async (req, res) => {
  try {
    const filter = {
      account: req.userData.account,
      deletedAt: null,
      user: req.userData._id,
    };

    const page = req.query.page;
    const perPage = req.query.perPage;

    const response = await notificationService.getNotificationByFilter(filter, page, perPage);

    const responseData = {
      notifications: response.data,
      totalCount: response.totalCount,
      page: page ? parseInt(page) : 0,
      perPage: perPage ? parseInt(perPage) : response.data.length,
    };

    res
      .status(HTTP_STATUS.OK)
      .json(successResponse(constantUtils.NOTIFICATION_FETCHED, responseData));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(errorResponse(error.message));
  }
};

/**
 * Update notification read status
 *
 * @param {Object} req
 * @param {Object} res
 */
exports.updateNotificationReadStatus = async (req, res) => {
  try {
    const { notificationRecipientId } = req.params;
    const { isRead } = req.body;

    const updateData = {
      isRead,
      readAt: isRead ? new Date() : null,
    };

    await notificationService.updateNotificationRecipientById(notificationRecipientId, updateData);

    res
      .status(HTTP_STATUS.OK)
      .json(successResponse(constantUtils.NOTIFICATION_READ_STATUS_UPDATED));
  } catch (error) {
    res
      .status(HTTP_STATUS.INTERNAL_SERVER_ERROR)
      .json(errorResponse(constantUtils.NOTIFICATION_NOT_FOUND));
  }
};

/**
 * Read all notifications
 *
 * @param {Object} req
 * @param {Object} res
 */
exports.readAllNotification = async (req, res) => {
  try {
    const { _id } = req.userData;

    const filter = {
      recipient: _id,
      isRead: false,
      deletedAt: null,
    };

    const updateData = {
      isRead: true,
      readAt: new Date(),
    };

    const result = await notificationService.updateNotificationRecipients(filter, updateData);

    // Check if any documents were actually updated
    if (result.matchedCount === 0) {
      return res
        .status(HTTP_STATUS.OK)
        .json(successResponse(constantUtils.NO_UNREAD_NOTIFICATIONS_FOUND));
    }

    res
      .status(HTTP_STATUS.OK)
      .json(successResponse(constantUtils.NOTIFICATION_READ_STATUS_UPDATED));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(errorResponse(error));
  }
};
