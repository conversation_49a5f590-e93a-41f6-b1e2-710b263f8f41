require('dotenv').config();
const admin = require('firebase-admin');
const { logger } = require('../app/utils/logger.utils');
const constantUtils = require('../app/utils/constants.utils');

const initializeFirebase = () => {
  try {
    if (admin.apps.length === 0) {
      const serviceAccount = JSON.parse(
        Buffer.from(process.env.FIREBASE_SERVICE_ACCOUNT_BASE64, 'base64').toString('utf8')
      );

      admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
      });

      logger.info(constantUtils.FIREBASE_INITIALIZED);
    }
    return admin;
  } catch (error) {
    logger.error(`${constantUtils.FIREBASE_INITIALIZATION_FAILED} : ${error}`);
    throw error;
  }
};

module.exports = { initializeFirebase, admin };
