{"name": "wtsenergy", "version": "1.0.1", "description": "", "main": "index.js", "scripts": {"test": "jest", "dev": "nodemon index.js", "start": "node index.js", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write '**/*.{js,jsx,ts,tsx,json,md}'", "prepare": "husky install", "pre-commit": "lint-staged", "seeder": "node ./app/seeders/index.js"}, "author": "", "license": "ISC", "dependencies": {"@sendgrid/mail": "^8.1.1", "adm-zip": "^0.5.10", "aws-sdk": "^2.1317.0", "axios": "^1.7.8", "bcryptjs": "^2.4.3", "bic-validator": "^1.27.0", "cors": "^2.8.5", "cron": "^4.3.1", "crypto-random-string": "^3.3.1", "dateformat": "^4.6.3", "dotenv": "^16.6.1", "exceljs": "^4.3.0", "express": "^4.21.2", "express-rate-limit": "^7.5.1", "express-validator": "^6.15.0", "file-size": "^1.0.0", "firebase-admin": "^13.5.0", "html-pdf-node": "^1.0.7", "ipware": "^2.0.0", "jsonwebtoken": "^9.0.2", "luxon": "^3.6.1", "mongodb": "^6.11.0", "mongoose": "^8.8.3", "morgan": "^1.10.0", "multer": "^2.0.2", "pdfkit-table": "^0.1.99", "rand-token": "^1.0.1", "sharp": "^0.33.5", "winston": "^3.17.0", "winston-mongodb": "^6.0.0"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "eslint": "^8.57.1", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "husky": "^8.0.3", "jest": "^30.0.5", "lint-staged": "^16.1.2", "nodemon": "^3.1.10", "prettier": "^2.8.7", "supertest": "^7.1.4"}, "engines": {"node": "22.17.1"}}