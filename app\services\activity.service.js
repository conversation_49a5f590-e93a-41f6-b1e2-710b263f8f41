const { toObjectId } = require('../utils/common.utils');
const Activity = require('../models/activity.model');

/**
 * Create Activity
 *
 * @param {*} Activity
 * @returns
 */
exports.createActivity = async requestData => await Activity.create(requestData);

/**
 * Get All Activity
 *
 * @returns
 */
exports.getAllActivity = async filter => {
  return Activity.find(filter)
    .populate([
      {
        path: 'scopeId',
        model: 'scope',
        select: 'name',
      },
      {
        path: 'project',
        select: 'title projectNumber',
      },
      {
        path: 'account',
        select: 'name',
      },
    ])
    .sort({ sortOrder: 1 });
};

/**
 * Update Activity
 *
 * @param {*} id
 * @param {*} update
 * @returns
 */
exports.updateActivity = async (id, update) =>
  Activity.findByIdAndUpdate(id, update, { new: true }).populate([
    {
      path: 'scopeId',
      model: 'scope',
      select: 'name',
    },
    {
      path: 'project',
      select: 'title projectNumber',
    },
    {
      path: 'account',
      select: 'name',
    },
  ]);

/**
 * Delete Activity
 *
 * @param {*} id
 * @param {*} deletedAt
 * @returns
 */
exports.deleteActivity = async (id, deletedAt) =>
  Activity.findByIdAndUpdate(id, { $set: deletedAt });

/**
 * Get Scope By Id
 *
 * @param {*} id
 * @returns
 */
exports.getActivityById = async id =>
  Activity.find({ $and: [{ _id: toObjectId(id) }, { deletedAt: null }] });

/**
 * Get Activity By projectId
 *
 * @param {*} projectId
 * @returns
 */
exports.getActivityByProjectId = async (
  filter = {},
  page = null,
  perPage = null,
  sort = { sortOrder: 1 }
) => {
  return await Activity.find(filter)
    .collation({ locale: 'en', strength: 2 })
    .sort(sort)
    .limit(perPage)
    .skip(page * perPage)
    .populate([
      {
        path: 'scopeId',
        model: 'scope',
        select: 'name',
      },
      {
        path: 'project',
        select: 'title projectNumber',
      },
      {
        path: 'account',
        select: 'name',
      },
    ]);
};

/**
 * Get Activity by project id and name
 *
 * @param {*} projectId
 * @param {*} name
 * @returns
 */
exports.getActivityByProjectIdAndName = async filter => Activity.findOne(filter);

/**
 * Delete All Project Activity
 *
 * @param {*} id
 * @param {*} deletedAt
 * @returns
 */
exports.deleteAllProjectActivity = async (projectId, deletedAt) => {
  return Activity.updateMany(
    { project: projectId },
    {
      $set: deletedAt,
    }
  );
};

exports.getCycleTimesByScope = async filter => {
  const pipeline = [
    { $match: filter },

    // join scope
    {
      $lookup: {
        from: 'scopes',
        localField: 'scopeId',
        foreignField: '_id',
        as: 'scope',
      },
    },
    { $unwind: '$scope' },

    // join shift-activities and keep non-deleted links
    {
      $lookup: {
        from: 'shift-activities',
        localField: '_id',
        foreignField: 'activity',
        as: 'sa',
      },
    },
    { $unwind: '$sa' },
    { $match: { 'sa.isDeleted': { $ne: true } } },

    // join shifts
    {
      $lookup: {
        from: 'shifts',
        localField: 'sa.shift',
        foreignField: '_id',
        as: 'shift',
      },
    },
    { $unwind: '$shift' },
    { $match: { 'shift.duration': { $type: 'string', $ne: '' } } },

    // parse "H:MM" -> minutes
    { $addFields: { _durParts: { $split: ['$shift.duration', ':'] } } },
    {
      $addFields: {
        _hours: { $toInt: { $arrayElemAt: ['$_durParts', 0] } },
        _mins: { $toInt: { $arrayElemAt: ['$_durParts', 1] } },
        durationMinutes: {
          $add: [
            { $multiply: [{ $toInt: { $arrayElemAt: ['$_durParts', 0] } }, 60] },
            { $toInt: { $arrayElemAt: ['$_durParts', 1] } },
          ],
        },
      },
    },

    // --- key step ---
    // collapse to one row per (scope, shiftId), so a shift counted once per scope
    {
      $group: {
        _id: { scope: '$scope.name', shiftId: '$shift._id' },
        oneShiftMinutes: { $first: '$durationMinutes' },
      },
    },

    // sum per scope
    {
      $group: {
        _id: '$_id.scope',
        totalMinutes: { $sum: '$oneShiftMinutes' },
      },
    },

    { $project: { _id: 0, scope: '$_id', totalMinutes: 1 } },
    { $sort: { scope: 1 } },
  ];

  return await Activity.aggregate(pipeline);
};
