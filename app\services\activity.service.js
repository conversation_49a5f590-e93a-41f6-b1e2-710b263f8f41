const { toObjectId } = require('../utils/common.utils');
const Activity = require('../models/activity.model');

/**
 * Create Activity
 *
 * @param {*} Activity
 * @returns
 */
exports.createActivity = async requestData => await Activity.create(requestData);

/**
 * Get All Activity
 *
 * @returns
 */
exports.getAllActivity = async filter => {
  return Activity.find(filter)
    .populate([
      {
        path: 'scopeId',
        model: 'scope',
        select: 'name',
      },
      {
        path: 'project',
        select: 'title projectNumber',
      },
      {
        path: 'account',
        select: 'name',
      },
    ])
    .sort({ sortOrder: 1 });
};

/**
 * Update Activity
 *
 * @param {*} id
 * @param {*} update
 * @returns
 */
exports.updateActivity = async (id, update) =>
  Activity.findByIdAndUpdate(id, update, { new: true }).populate([
    {
      path: 'scopeId',
      model: 'scope',
      select: 'name',
    },
    {
      path: 'project',
      select: 'title projectNumber',
    },
    {
      path: 'account',
      select: 'name',
    },
  ]);

/**
 * Delete Activity
 *
 * @param {*} id
 * @param {*} deletedAt
 * @returns
 */
exports.deleteActivity = async (id, deletedAt) =>
  Activity.findByIdAndUpdate(id, { $set: deletedAt });

/**
 * Get Scope By Id
 *
 * @param {*} id
 * @returns
 */
exports.getActivityById = async id =>
  Activity.find({ $and: [{ _id: toObjectId(id) }, { deletedAt: null }] });

/**
 * Get Activity By projectId
 *
 * @param {*} projectId
 * @returns
 */
exports.getActivityByProjectId = async (
  filter = {},
  page = null,
  perPage = null,
  sort = { sortOrder: 1 }
) => {
  return await Activity.find(filter)
    .collation({ locale: 'en', strength: 2 })
    .sort(sort)
    .limit(perPage)
    .skip(page * perPage)
    .populate([
      {
        path: 'scopeId',
        model: 'scope',
        select: 'name',
      },
      {
        path: 'project',
        select: 'title projectNumber',
      },
      {
        path: 'account',
        select: 'name',
      },
    ]);
};

/**
 * Get Activity by project id and name
 *
 * @param {*} projectId
 * @param {*} name
 * @returns
 */
exports.getActivityByProjectIdAndName = async filter => Activity.findOne(filter);

/**
 * Delete All Project Activity
 *
 * @param {*} id
 * @param {*} deletedAt
 * @returns
 */
exports.deleteAllProjectActivity = async (projectId, deletedAt) => {
  return Activity.updateMany(
    { project: projectId },
    {
      $set: deletedAt,
    }
  );
};

exports.getCycleTimesByScope = async filter => {
  const pipeline = [
    {
      $match: {
        project: ObjectId('68d4ea15a6266f7c5f0e20bb'),
        deletedAt: null,
      },
    },
    {
      $lookup: {
        from: 'scopes',
        localField: 'scopeId',
        foreignField: '_id',
        as: 'scope',
        pipeline: [
          {
            $match: {
              deletedAt: null,
            },
          },
        ],
      },
    },
    { $unwind: '$scope' },
    {
      $lookup: {
        from: 'shift-activities',
        localField: '_id',
        foreignField: 'activity',
        as: 'sa',
        pipeline: [
          {
            $match: {
              location: ObjectId('68d4ea29a6266f7c5f0e21f5'),
              deletedAt: null,
            },
          },
        ],
      },
    },
    { $unwind: '$sa' },
    {
      $lookup: {
        from: 'shifts',
        localField: 'sa.shift',
        foreignField: '_id',
        as: 'shift',
        pipeline: [
          {
            $match: {
              deletedAt: null,
            },
          },
        ],
      },
    },
    { $unwind: '$shift' },
    {
      $match: {
        'sa.endTime': { $type: 'string', $ne: '' },
      },
    },
    {
      $addFields: {
        activityEndTime: {
          $dateFromString: {
            dateString: '$sa.endTime',
          },
        },
        shiftStartTime: {
          $dateFromString: {
            dateString: '$shift.startDate',
          },
        },
        shiftId: '$shift._id',
        activityId: '$sa._id',
      },
    },
    {
      $group: {
        _id: '$shiftId',
        scope: { $first: '$scope.name' },
        shiftStartTime: {
          $first: '$shiftStartTime',
        },
        activities: {
          $push: {
            activityId: '$activityId',
            activityName: '$name',
            endTime: '$activityEndTime',
            scopeName: '$scope.name',
          },
        },
      },
    },
    {
      $addFields: {
        activities: {
          $sortArray: {
            input: '$activities',
            sortBy: { endTime: 1 },
          },
        },
      },
    },
    {
      $addFields: {
        activitiesWithDuration: {
          $map: {
            input: {
              $range: [0, { $size: '$activities' }],
            },
            as: 'index',
            in: {
              $let: {
                vars: {
                  currentActivity: {
                    $arrayElemAt: ['$activities', '$$index'],
                  },
                  prevActivity: {
                    $cond: {
                      if: { $eq: ['$$index', 0] },
                      then: null,
                      else: {
                        $arrayElemAt: [
                          '$activities',
                          {
                            $subtract: ['$$index', 1],
                          },
                        ],
                      },
                    },
                  },
                },
                in: {
                  activityId: '$$currentActivity.activityId',
                  activityName: '$$currentActivity.activityName',
                  scopeName: '$$currentActivity.scopeName',
                  endTime: '$$currentActivity.endTime',
                  durationMinutes: {
                    $divide: [
                      {
                        $subtract: [
                          '$$currentActivity.endTime',
                          {
                            $cond: {
                              if: {
                                $eq: ['$$prevActivity', null],
                              },
                              then: '$shiftStartTime',
                              else: '$$prevActivity.endTime',
                            },
                          },
                        ],
                      },
                      60000,
                    ],
                  },
                },
              },
            },
          },
        },
      },
    },
    { $unwind: '$activitiesWithDuration' },
    {
      $addFields: {
        'activitiesWithDuration.durationMinutes': {
          $max: [0, '$activitiesWithDuration.durationMinutes'],
        },
      },
    },
    {
      $group: {
        _id: '$activitiesWithDuration.scopeName',
        totalMinutes: {
          $sum: '$activitiesWithDuration.durationMinutes',
        },
      },
    },
    {
      $addFields: {
        totalHours: {
          $floor: { $divide: ['$totalMinutes', 60] },
        },
        remainingMinutes: {
          $mod: ['$totalMinutes', 60],
        },
      },
    },
    {
      $addFields: {
        formattedDuration: {
          $concat: [{ $toString: '$totalHours' }, 'h ', { $toString: '$remainingMinutes' }, 'min'],
        },
      },
    },
    {
      $project: {
        _id: 0,
        scope: '$_id',
        totalMinutes: {
          $round: ['$totalMinutes', 0],
        },
        duration: '$formattedDuration',
      },
    },
    { $sort: { scope: 1 } },
  ];

  console.log(pipeline, JSON.stringify(pipeline));

  return await Activity.aggregate(pipeline);
};
