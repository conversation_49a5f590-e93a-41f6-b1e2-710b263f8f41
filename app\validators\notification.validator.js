const { body, param, constantUtils } = require('./parent.validator');

exports.updateReadStatusValidationRule = () => {
  return [
    param('notificationRecipientId')
      .isMongoId()
      .withMessage(constantUtils.INVALID_NOTIFICATION_RECIPIENT_ID),
    body('isRead')
      .isBoolean()
      .withMessage(constantUtils.IS_READ_BOOLEAN)
      .notEmpty()
      .withMessage(constantUtils.IS_READ_REQUIRED),
  ];
};
