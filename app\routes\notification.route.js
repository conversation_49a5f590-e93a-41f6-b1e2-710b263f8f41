const router = require('express').Router();
const { verifyToken, authAccount } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');
const notificationController = require('../controllers/notification.controller');
const notificationValidator = require('../validators/notification.validator');

router.get('', verifyToken, authAccount, notificationController.getAllNotification);

router.patch(
  '/recipient/read-all',
  verifyToken,
  authAccount,
  notificationController.readAllNotification
);

router.patch(
  '/recipient/:notificationRecipientId',
  verifyToken,
  authAccount,
  notificationValidator.updateReadStatusValidationRule(),
  validate,
  notificationController.updateNotificationReadStatus
);

module.exports = router;
