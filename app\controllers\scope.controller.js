require('dotenv').config();

// services
const scopeService = require('../services/scope.service');
const activityService = require('../services/activity.service');
const reportService = require('../services/report.service');

// Utils
const constantUtils = require('../utils/constants.utils');
const responseUtils = require('../utils/response.utils');
const HTTP_STATUS = require('../utils/status-codes');
const { commonUtils } = require('../validators/parent.validator');

/**
 * Create Scope
 *
 * @param {*} req
 * @param {*} res
 */
exports.createScope = async (req, res) => {
  try {
    const { account } = req.userData;
    req.body.account = account;

    const { name, project } = req.body;
    const filterData = { name, project, account: account.toString() };
    const exist = await scopeService.getScopeByProjectIdAndName(filterData);

    if (exist.length > 0) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.SCOPE_EXIST));
    }
    const scopeData = await scopeService.createScope(req.body);

    res.status(200).json(responseUtils.successResponse(constantUtils.CREATE_SCOPE, scopeData));
  } catch (err) {
    const errCode = err.code ?? 500;
    res.status(errCode).json(responseUtils.errorResponse(err.message));
  }
};

/**
 * Update Scope
 *
 * @param {*} req
 * @param {*} res
 */
exports.updateScope = async (req, res) => {
  try {
    const id = req.params.id;
    const exist = await scopeService.getScopeById(id);

    if (exist.length == 0) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.NO_SCOPE));
    }

    req.body.account = req.userData.account;

    const scopeData = await scopeService.updateScope(id, req.body);

    res.status(200).json(responseUtils.successResponse(constantUtils.UPDATE_SCOPE, scopeData));
  } catch (err) {
    const errCode = err.code ?? 500;
    res.status(errCode).json(responseUtils.errorResponse(err.message));
  }
};

/**
 * Delete Scope
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.deleteScope = async (req, res) => {
  try {
    const id = req.params.id;
    const exist = await scopeService.getScopeById(id);

    if (!exist || exist.length == 0) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(responseUtils.errorResponse(constantUtils.NO_SCOPE));
    }

    const allReportsId = exist[0].reports.map(report => report._id);
    const getAllReports = await reportService.getAllReportsByReportId({
      _id: { $in: allReportsId },
      deletedAt: null,
    });

    if (getAllReports.length > 0) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(responseUtils.errorResponse(constantUtils.SCOPE_HAS_REPORTS));
    }

    const activityExist = await activityService.getAllActivity({ scopeId: id, deletedAt: null });

    if (activityExist.length > 0) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(responseUtils.errorResponse(constantUtils.SCOPE_HAS_ACTIVITY));
    }

    const scopeData = await scopeService.deleteScope(id, req.deletedAt);

    res
      .status(HTTP_STATUS.OK)
      .json(responseUtils.successResponse(constantUtils.DELETE_SCOPE, scopeData));
  } catch (err) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(responseUtils.errorResponse(err.message));
  }
};

/**
 * Get scope with activity
 *
 * @param {Object} req
 * @param {Object} res
 * @returns
 */
exports.getScopeWithActivityDetails = async (req, res) => {
  try {
    const { id } = req.params;

    const activities = await activityService.getCycleTimesByScope({
      project: commonUtils.toObjectId(id),
      deletedAt: null,
    });
    res
      .status(HTTP_STATUS.OK)
      .json(responseUtils.successResponse(constantUtils.ALL_ACTIVITY_LIST, activities));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(responseUtils.errorResponse(error.message));
  }
};
