// library
const express = require('express');
const routes = express.Router();

// middleware
const { verifyToken } = require('../middlewares/auth.middleware');

// controller
const equipmentOrderController = require('../controllers/equipment-order.controller');

routes.post(
  '/shopping-cart',
  verifyToken,
  equipmentOrderController.createEquipmentOrderFromShoppingCartV3
);

module.exports = routes;
