const ProjectDocument = require('../models/project-document.model');

exports.up = async () => {
  try {
    // Project documents (quality_update -> quality_alert)
    const qualityDocumentTypeCount = await ProjectDocument.updateMany(
      { type: 'quality_update' },
      { type: 'quality_alert' }
    );
    console.log(
      `Successfully updated ${qualityDocumentTypeCount.modifiedCount} quality update documents to quality alert documents`
    );

    // Project documents (safety_notification, safety_update -> safety_alert)
    const safetyDocumentTypeCount = await ProjectDocument.updateMany(
      { type: { $in: ['safety_notification', 'safety_update'] } },
      { type: 'safety_alert' }
    );
    console.log(
      `Successfully updated ${safetyDocumentTypeCount.modifiedCount} safety_notification, safety_update to safety_alert documents`
    );
  } catch (error) {
    console.error(error);
  }
};
