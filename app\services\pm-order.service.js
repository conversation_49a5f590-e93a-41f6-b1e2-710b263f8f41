const PmOrder = require('../models/pm-order.model');
const EquipmentOrder = require('../models/equipment-order.model');
const EquipmentOrderHistory = require('../models/equipment-order-history.model');

exports.createPMOrder = async requestData => {
  return PmOrder.create(requestData);
};

exports.updatePMOrder = async (id, data, session) => {
  return await PmOrder.findByIdAndUpdate(id, { $set: data }, { new: true, session });
};

exports.updateEquipmentOrder = async (id, data) => {
  return await EquipmentOrder.findByIdAndUpdate(id, { $set: data }, { new: true });
};

exports.searchPMOrder = async filter => {
  return await PmOrder.findOne(filter);
};

exports.getPMOrderById = async id => {
  return await PmOrder.findById(id).populate([
    {
      path: 'project',
      select: { title: 1, _id: 1, projectNumber: 1 },
      strictPopulate: false,
    },
  ]);
};

exports.getPMOrderList = async (filter, page, perPage, search) => {
  const searchParts = search?.trim().split(/\s+/) || [];
  const firstName = searchParts[0] || '';
  const lastName = searchParts.slice(1).join(' ') || '';

  let aggregateFunction = [
    {
      $match: filter,
    },
    {
      $lookup: {
        from: 'projects',
        localField: 'project',
        foreignField: '_id',
        as: 'project',
        pipeline: [{ $project: { title: 1, projectNumber: 1 } }],
      },
    },
    {
      $unwind: '$project',
    },
    {
      $lookup: {
        from: 'accounts',
        localField: 'account',
        foreignField: '_id',
        pipeline: [{ $project: { _id: 1, name: 1 } }],
        as: 'account',
      },
    },
    {
      $unwind: '$account',
    },
    {
      $lookup: {
        from: 'users',
        localField: 'createdBy',
        foreignField: '_id',
        pipeline: [{ $project: { _id: 1, callingName: 1, firstName: 1, lastName: 1 } }],
        as: 'createdBy',
      },
    },
    {
      $unwind: '$createdBy',
    },
    {
      $lookup: {
        from: 'pm-order-manage-equipments',
        localField: '_id',
        foreignField: 'pmOrder',
        pipeline: [
          {
            $match: {
              status: filter.status,
              deletedAt: null,
            },
          },
          // Join with equipment-orders to check if they are rejected
          {
            $lookup: {
              from: 'equipment-orders',
              localField: '_id',
              foreignField: 'pmOrderManageEquipment',
              as: 'relatedEquipmentOrders',
            },
          },
          {
            $match: {
              $or: [
                { relatedEquipmentOrders: { $size: 0 } },
                { 'relatedEquipmentOrders.status': { $ne: 'rejected' } },
              ],
            },
          },
        ],
        as: 'pmManageEquipment',
      },
    },
    {
      $unwind: '$pmManageEquipment',
    },
    {
      $lookup: {
        from: 'equipment-types',
        localField: 'pmManageEquipment.equipmentType',
        foreignField: '_id',
        as: 'equipmentTypeDetails',
      },
    },
    {
      $unwind: '$equipmentTypeDetails',
    },
    {
      $lookup: {
        from: 'equipment-orders',
        localField: '_id',
        foreignField: 'pmOrderId',
        as: 'equipmentOrders',
        pipeline: [
          {
            $lookup: {
              from: 'shopping-carts',
              localField: 'shoppingCart',
              foreignField: '_id',
              pipeline: [
                {
                  $project: {
                    title: 1,
                    fromDate: 1,
                    toDate: 1,
                  },
                },
              ],
              as: 'shoppingCart',
            },
          },
          {
            $unwind: {
              path: '$shoppingCart',
              preserveNullAndEmptyArrays: true,
            },
          },
        ],
      },
    },
    {
      $match: {
        $or: [
          {
            orderNumber: search ? { $regex: search, $options: 'i' } : { $exists: true },
          },
          {
            'createdBy.firstName': search ? { $regex: search, $options: 'i' } : { $exists: true },
          },
          {
            'createdBy.lastName': search ? { $regex: search, $options: 'i' } : { $exists: true },
          },
          {
            'createdBy.callingName': search ? { $regex: search, $options: 'i' } : { $exists: true },
          },
          {
            'equipmentOrders.shoppingCart.title': search
              ? { $regex: search, $options: 'i' }
              : { $exists: true },
          },
          ...(firstName && lastName
            ? [
                {
                  $and: [
                    { 'createdBy.firstName': { $regex: `^${firstName}$`, $options: 'i' } },
                    { 'createdBy.lastName': { $regex: `^${lastName}$`, $options: 'i' } },
                  ],
                },
              ]
            : []),
        ],
      },
    },
    {
      $lookup: {
        from: 'equipment',
        localField: 'equipmentTypeDetails._id',
        foreignField: 'equipmentType',
        as: 'associatedEquipment',
        pipeline: [
          {
            $match: {
              equipmentImage: { $exists: true, $ne: [] },
            },
          },
          { $sort: { createdAt: -1 } },
          {
            $addFields: {
              lastEquipmentImage: { $arrayElemAt: ['$equipmentImage', -1] },
            },
          },
        ],
      },
    },
    {
      $lookup: {
        from: 'equipment',
        localField: 'pmManageEquipment.equipment',
        foreignField: '_id',
        as: 'equipmentDetails',
        pipeline: [
          {
            $project: {
              _id: 1,
              name: 1,
              equipmentNumber: 1,
              qrCode: 1,
              equipmentImage: 1,
            },
          },
        ],
      },
    },
    {
      $lookup: {
        from: 'equipment-order-histories',
        localField: 'pmManageEquipment.equipment',
        foreignField: 'equipment',
        let: { pmOrderManageEquipment: '$pmManageEquipment._id' },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ['$pmOrderManageEquipment', '$$pmOrderManageEquipment'] },
                  { $eq: ['$deletedAt', null] },
                ],
              },
            },
          },
          {
            $project: {
              _id: 0,
              equipment: 1,
              status: 1,
              wmDispatchQuantity: 1,
            },
          },
          {
            $lookup: {
              from: 'equipment',
              localField: 'equipment',
              foreignField: '_id',
              as: 'equipment',
              pipeline: [
                {
                  $project: {
                    _id: 1,
                    name: 1,
                    equipmentNumber: 1,
                    serialNumber: 1,
                    equipmentImage: 1,
                  },
                },
              ],
            },
          },
          {
            $unwind: '$equipment',
          },
        ],
        as: 'equipmentOrderHistory',
      },
    },
    {
      $lookup: {
        from: 'pm-order-manage-equipments',
        localField: '_id',
        foreignField: 'pmOrder',
        pipeline: [
          {
            $match: {
              status: filter.status,
              deletedAt: null,
            },
          },
          // Join with equipment-orders to check if they are rejected
          {
            $lookup: {
              from: 'equipment-orders',
              localField: '_id',
              foreignField: 'pmOrderManageEquipment',
              as: 'relatedEquipmentOrders',
            },
          },
          // Filter out pm-order-manage-equipments that have rejected equipment orders
          {
            $match: {
              $or: [
                { relatedEquipmentOrders: { $size: 0 } }, // No related equipment orders (legacy)
                { 'relatedEquipmentOrders.status': { $ne: 'rejected' } }, // Related equipment orders are not rejected
              ],
            },
          },
        ],
        as: 'pmManageEquipmentArray',
      },
    },
    {
      $lookup: {
        from: 'users',
        localField: 'pmManageEquipment.pmComments.user',
        foreignField: '_id',
        as: 'engineerCommentUserDetails',
        pipeline: [
          {
            $project: {
              _id: 1,
              callingName: 1,
              firstName: 1,
              lastName: 1,
            },
          },
        ],
      },
    },
    {
      $addFields: {
        'pmManageEquipment.pmComments': {
          $map: {
            input: '$pmManageEquipment.pmComments',
            as: 'comment',
            in: {
              user: {
                $arrayElemAt: [
                  {
                    $filter: {
                      input: '$engineerCommentUserDetails',
                      as: 'userDetails',
                      cond: { $eq: ['$$userDetails._id', '$$comment.user'] },
                    },
                  },
                  0,
                ],
              },
              time: '$$comment.time',
              status: '$$comment.status',
              comment: '$$comment.comment',
            },
          },
        },
      },
    },
    {
      $lookup: {
        from: 'users',
        localField: 'pmManageEquipment.wmComments.user',
        foreignField: '_id',
        as: 'instructionUserDetails',
        pipeline: [
          {
            $project: {
              _id: 1,
              callingName: 1,
              firstName: 1,
              lastName: 1,
            },
          },
        ],
      },
    },
    {
      $addFields: {
        'pmManageEquipment.wmComments': {
          $map: {
            input: '$pmManageEquipment.wmComments',
            as: 'instructionItem',
            in: {
              user: {
                $arrayElemAt: [
                  {
                    $filter: {
                      input: '$instructionUserDetails',
                      as: 'userDetails',
                      cond: { $eq: ['$$userDetails._id', '$$instructionItem.user'] },
                    },
                  },
                  0,
                ],
              },
              time: '$$instructionItem.time',
              status: '$$instructionItem.status',
              comment: '$$instructionItem.comment',
            },
          },
        },
      },
    },
    {
      $lookup: {
        from: 'users',
        localField: 'pmManageEquipment.remark.user',
        foreignField: '_id',
        as: 'remarkUserDetails',
        pipeline: [
          {
            $project: {
              _id: 1,
              callingName: 1,
              firstName: 1,
              lastName: 1,
            },
          },
        ],
      },
    },
    {
      $addFields: {
        'pmManageEquipment.remark': {
          $cond: {
            if: { $isArray: '$pmManageEquipment.remark' },
            then: {
              $map: {
                input: '$pmManageEquipment.remark',
                as: 'remarkItem',
                in: {
                  user: {
                    $arrayElemAt: [
                      {
                        $filter: {
                          input: '$remarkUserDetails',
                          as: 'userDetails',
                          cond: { $eq: ['$$userDetails._id', '$$remarkItem.user'] },
                        },
                      },
                      0,
                    ],
                  },
                  time: '$$remarkItem.time',
                  status: '$$remarkItem.status',
                  comment: '$$remarkItem.comment',
                },
              },
            },
            else: [],
          },
        },
      },
    },
    {
      $group: {
        _id: '$_id',
        orderNumber: {
          $first: '$orderNumber',
        },
        orderBy: {
          $first: {
            id: '$createdBy._id',
            callingName: '$createdBy.callingName',
            firstName: '$createdBy.firstName',
            lastName: '$createdBy.lastName',
          },
        },
        shoppingCartTitle: { $first: '$equipmentOrders.shoppingCart.title' },
        shoppingCartData: { $first: '$equipmentOrders.shoppingCart' },
        equipmentTypeData: {
          $addToSet: {
            equipmentTypeId: '$equipmentTypeDetails._id',
            isTemporary: { $ifNull: ['$equipmentTypeDetails.isTemporary', false] },
            typeName: '$equipmentTypeDetails.type',
            equipmentTypeImage: {
              $first: '$associatedEquipment.lastEquipmentImage',
            },
            manageId: '$pmManageEquipment._id',
            comments: {
              wmComments: '$pmManageEquipment.wmComments',
              pmComments: '$pmManageEquipment.pmComments',
            },
            pmRequestedQuantity: '$pmManageEquipment.pmRequestedQuantity',
            wmApprovedQuantity: '$pmManageEquipment.wmApprovedQuantity',
            remark: '$pmManageEquipment.remark',
            pmReceivedQuantity: '$pmManageEquipment.pmReceivedQuantity',
            status: '$pmManageEquipment.status',
            fromPeriod: '$pmManageEquipment.fromPeriod',
            toPeriod: '$pmManageEquipment.toPeriod',
            linkedEquipment: '$equipmentOrderHistory',
            createdAt: '$equipmentTypeDetails.createdAt',
          },
        },
        totalRequestedQuantity: {
          $sum: '$pmManageEquipment.pmRequestedQuantity',
        },
        totalApprovedQuantity: {
          $sum: '$pmManageEquipment.wmApprovedQuantity',
        },
        totalItems: {
          $first: {
            $size: '$pmManageEquipmentArray',
          },
        },
        account: {
          $first: '$account',
        },
        project: {
          $first: '$project',
        },
        status: {
          $first: '$status',
        },
        createdAt: {
          $first: '$createdAt',
        },
        updatedAt: {
          $first: '$updatedAt',
        },
      },
    },
    {
      $unwind: '$equipmentTypeData',
    },
    {
      $sort: { 'equipmentTypeData.createdAt': -1 },
    },
    {
      $group: {
        _id: '$_id',
        orderNumber: { $first: '$orderNumber' },
        shoppingCartTitle: { $first: '$shoppingCartTitle' },
        shoppingCartData: { $first: '$shoppingCartData' },
        orderBy: { $first: '$orderBy' },
        equipmentTypeData: { $push: '$equipmentTypeData' },
        totalRequestedQuantity: { $first: '$totalRequestedQuantity' },
        totalApprovedQuantity: { $first: '$totalApprovedQuantity' },
        totalItems: { $first: '$totalItems' },
        account: { $first: '$account' },
        project: { $first: '$project' },
        status: { $first: '$status' },
        createdAt: { $first: '$createdAt' },
        updatedAt: { $first: '$updatedAt' },
      },
    },
    {
      $sort: { createdAt: -1 },
    },
  ];

  if (page && perPage) {
    aggregateFunction.push(
      {
        $skip: parseInt(page) * parseInt(perPage),
      },
      {
        $limit: parseInt(perPage),
      }
    );
  }

  aggregateFunction.push(
    {
      $group: {
        _id: '$project._id',
        projectName: {
          $first: '$project.title',
        },
        orderData: {
          $push: {
            orderId: '$_id',
            orderNumber: '$orderNumber',
            orderBy: '$orderBy',
            shoppingCartTitle: '$shoppingCartTitle',
            shoppingCartData: { $first: '$shoppingCartData' },
            equipmentTypeData: '$equipmentTypeData',
            totalRequestedQuantity: '$totalRequestedQuantity',
            totalApprovedQuantity: '$totalApprovedQuantity',
            totalApprovedItems: '$totalItems',
            account: '$account',
            orderStatus: '$status',
            createdAt: '$createdAt',
            updatedAt: '$updatedAt',
          },
        },
      },
    },
    {
      $project: {
        _id: 1,
        projectName: 1,
        orderData: 1,
      },
    },
    {
      $sort: { 'orderData.createdAt': -1 },
    }
  );

  return await PmOrder.aggregate(aggregateFunction);
};

/**
 * Get Order Status Count
 *
 * @param {*} filter
 * @returns
 */
exports.getOrderStatusCountList = async filter => {
  let pmOrdersCount = await PmOrder.aggregate([
    {
      $match: filter,
    },
    {
      $group: {
        _id: '$status',
        count: {
          $sum: 1,
        },
      },
    },
  ]);

  let equipmentOrdersCount = await EquipmentOrder.aggregate([
    {
      $match: filter,
    },
    {
      $group: {
        _id: '$status',
        count: {
          $sum: 1,
        },
      },
    },
  ]);

  const pmOrderStatus = [
    'open',
    'requested',
    'approved',
    'rejected',
    'partially-pre-transit',
    'pre-transit',
    'in-transit',
    'check-in',
    'check-out',
    'pre-check-out',
    'partially-check-in',
    'partially-check-out',
    'partially-in-stock',
    'in-stock',
    'missing',
  ];

  let pmOrderFinalData = await exports.prepareOrderStatusCount(pmOrdersCount, pmOrderStatus);

  let equipmentOrderStatus = ['pending', 'queue', 'requested', 'rejected'];
  let equipmentOrderFinalData = await exports.prepareOrderStatusCount(
    equipmentOrdersCount,
    equipmentOrderStatus
  );

  return {
    'engineer-order-status-count': equipmentOrderFinalData,
    'pm-order-status-count': pmOrderFinalData,
  };
};

/**
 * Prepare Order Status Count
 *
 * @param {*} orderCount
 * @param {*} orderStatus
 * @returns
 */
exports.prepareOrderStatusCount = async (orderCount, orderStatus) => {
  let existStatus = [];
  Object.keys(orderCount).forEach(key => {
    if (orderStatus.includes(orderCount[key]._id)) {
      existStatus.push(orderCount[key]._id);
    }
  });

  let notExistStatus = [];
  for (let key in orderStatus) {
    if (!existStatus.includes(orderStatus[key])) {
      notExistStatus.push(orderStatus[key]);
    }
  }

  let orderFinalData = {};
  for (let key in notExistStatus) {
    orderFinalData[notExistStatus[key]] = 0;
  }

  for (let key in orderCount) {
    if (orderStatus.includes(orderCount[key]._id)) {
      orderFinalData[orderCount[key]._id] = orderCount[key].count;
    }
  }
  return orderFinalData;
};

exports.getEquipmentOrderHistoryByFilter = async (filter, page, perPage) => {
  return await EquipmentOrderHistory.find(filter)
    .populate([
      {
        path: 'pmOrder',
        select: { orderNumber: 1, _id: 0, status: 1, fromDate: 1, toDate: 1, comments: 1 },
        strictPopulate: false,
      },
      {
        path: 'account',
        select: { name: 1, _id: 0 },
        strictPopulate: false,
      },
      {
        path: 'equipment',
        select: {
          name: 1,
          _id: 1,
          equipmentNumber: 1,
          serialNumber: 1,
          status: 1,
          fromDate: 1,
          toDate: 1,
        },
        strictPopulate: false,
      },
      {
        path: 'equipmentType',
        select: { type: 1, _id: 0 },
        strictPopulate: false,
      },
      {
        path: 'pmOrderManageEquipment',
        select: { status: 1, _id: 0 },
        strictPopulate: false,
      },
    ])
    .select({ createdBy: 0, updatedAt: 0, __v: 0, deletedBy: 0, deletedAt: 0, updatedBy: 0 })
    .limit(parseInt(perPage))
    .skip(parseInt(page) * parseInt(perPage));
};

/**
 * Reject PM Order
 *
 * @param {*} id
 * @returns
 */
exports.rejectPMOrder = async id => {
  return await PmOrder.findByIdAndDelete(id);
};

/**
 * get pm orders projects
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.getPMOrdersProjects = async (filter, page, perPage, sort, search) => {
  let aggregateFunction = [
    {
      $match: filter,
    },
    {
      $lookup: {
        from: 'projects',
        localField: 'project',
        foreignField: '_id',
        as: 'project',
        pipeline: [
          ...(search ? [{ $match: { title: { $regex: search, $options: 'i' } } }] : []),
          { $project: { title: 1, projectNumber: 1 } },
        ],
      },
    },
    {
      $unwind: '$project',
    },
    {
      $group: {
        _id: '$project._id',
        projectName: {
          $first: '$project.title',
        },
        projectNumber: { $first: '$project.projectNumber' },
        totalOrders: { $sum: 1 },
        createdAt: { $max: '$createdAt' },
        updatedAt: { $max: '$updatedAt' },
      },
    },
    {
      $addFields: {
        projectName: {
          $concat: [
            { $ifNull: ['$projectNumber', ''] },
            {
              $cond: {
                if: { $ne: ['$projectNumber', ''] },
                then: ' - ',
                else: '',
              },
            },
            { $ifNull: ['$projectName', ''] },
          ],
        },
      },
    },
    {
      $sort: {
        createdAt: sort,
      },
    },
  ];

  if (page && perPage) {
    aggregateFunction.push(
      {
        $skip: parseInt(page) * parseInt(perPage),
      },
      {
        $limit: parseInt(perPage),
      }
    );
  }
  return await PmOrder.aggregate(aggregateFunction);
};

/**
 * get PM Orders
 *
 * @param {*} filter
 */
exports.getPmOrders = async filter => {
  return await PmOrder.find(filter);
};

/**
 * Find pm orders with custom select options
 *
 * @param {Object} filter
 * @param {Object|String} select
 * @returns {Promise}
 */
exports.findPmOrdersWithCustomSelect = async (filter, select = null) => {
  const query = PmOrder.find(filter);

  if (select) query.select(select);

  return await query;
};
