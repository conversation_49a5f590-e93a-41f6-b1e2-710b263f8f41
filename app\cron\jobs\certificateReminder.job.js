// Packages
const { <PERSON>ronJob } = require('cron');

// Services
const uploadCertificateService = require('../../services/upload-certificate.service');
const memberService = require('../../services/member.service');

// Utils
const commonFunctionsUtils = require('../../utils/common-function.utils');
const mailerUtils = require('../../utils/mailer.utils');
const constantsUtils = require('../../utils/constants.utils');

/** Normalize a date to UTC midnight */
function normalizeUtcDate(date) {
  return new Date(Date.UTC(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate()));
}

/** Add N days in UTC */
function addDaysUtc(baseDate, days) {
  return normalizeUtcDate(new Date(baseDate.getTime() + days * global.constant.DAY_CONVERTOR));
}

const certificateReminderJob = new CronJob(process.env.CERTIFICATE_CRON_SCHEDULE, async () => {
  console.log(constantsUtils.CERTIFICATE_REMAINDER_CRON_STARTED, new Date().toISOString());

  try {
    // Current UTC date normalized to midnight
    const now = new Date();
    const today = normalizeUtcDate(now);

    // Define reminder windows
    const reminderWindows = [
      { offset: -14, displayDays: -15, label: 'expired 15 days ago' },
      { offset: 0, displayDays: 0, label: 'expires today' },
      { offset: 14, displayDays: 15, label: 'expires in 15 days' },
      { offset: 29, displayDays: 30, label: 'expires in 30 days' },
      { offset: 60, displayDays: 60, label: 'expires in 60 days' },
    ];

    // Compute target UTC dates
    const targets = reminderWindows.map(w => ({
      ...w,
      targetDate: addDaysUtc(today, w.offset),
    }));

    // Fetch all active approved certificates
    const certificates = await uploadCertificateService.getUserUploadCertificate({
      isActive: true,
      status: 'approved',
      internal: false,
      deletedAt: null,
    });

    for (const cert of certificates) {
      const { user, endDate, name } = cert;
      if (!endDate || !user?.isActive || !user.email) continue;

      const memberProjects = await memberService.getAllMember({
        user: user._id,
        deletedAt: null,
      });

      const isAssigned = memberProjects.some(m => m.project?.isActive);

      if (!isAssigned) continue;

      const expiryDate = normalizeUtcDate(new Date(endDate));

      const match = targets.find(t => t.targetDate.getTime() === expiryDate.getTime());
      if (!match) continue;

      const { displayDays } = match;

      const commonTemplateData = {
        expiryDate: commonFunctionsUtils.formatDate(endDate),
        certificateName: name,
        daysAfterExpiry: displayDays < 0 ? `${Math.abs(displayDays)} day(s)` : '',
        daysRemaining: displayDays >= 0 ? `${displayDays} day(s)` : '',
        userFullName: `${user.callingName} ${user.lastName}`,
        email: user.email,
        supportTeamEmail: process.env.SUPPORT_TEAM_EMAIL,
        currentYear: new Date().getUTCFullYear(),
        bestRegards: process.env.BEST_REGARDS,
        organizationName: process.env.ORGANIZATION_NAME,
      };

      const template =
        displayDays < 0
          ? process.env.SENDGRID_CERTIFICATE_ALERT_REMAINDER
          : process.env.SENDGRID_CERTIFICATE_EXPIRY_REMAINDER;

      const templateData = {
        ...commonTemplateData,
        logo:
          displayDays < 0
            ? global.constant.CERTIFICATE_EXPIRED_LOGO
            : global.constant.CERTIFICATE_ALERT_LOGO,
      };
      // Send email
      await mailerUtils.sendMailer(user.email.toString(), template, templateData);
    }

    console.log(constantsUtils.CERTIFICATE_REMAINDER_CRON_COMPLETED);
  } catch (error) {
    console.error(constantsUtils.ERROR_CERTIFICATE_REMAINDER_CRON, error);
  }
});

module.exports = certificateReminderJob;
