const mongoose = require('mongoose');

const Scope = new mongoose.Schema(
  {
    name: {
      type: String,
    },
    sortOrder: {
      type: Number,
      default: null,
    },
    project: {
      type: mongoose.Types.ObjectId,
      ref: 'project',
    },
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
    },
    defaultIdentifier: {
      type: String,
      default: global.constant.NORMAL_DATA_IDENTIFIER, // DEFAULT_DATA_IDENTIFIER: for default project, NORMAL_DATA_IDENTIFIER: for normal project
    },
    reports: [
      {
        type: mongoose.Types.ObjectId,
        ref: 'report',
      },
    ],
    isDeletable: {
      type: Boolean,
      default: true,
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
    isDefault: {
      type: Boolean,
      default: false,
    },
    colorCode: {
      type: String,
      default: '#FF6600',
    },
  },
  { timestamps: true }
);

module.exports = mongoose.model('scope', Scope);
