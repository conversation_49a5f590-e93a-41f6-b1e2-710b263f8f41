const express = require('express');
const routes = express.Router();
const { verifyToken } = require('../middlewares/auth.middleware');
const validator = require('../validators/auth.validator');
const authController = require('../controllers/auth.controller');

routes.post('/login', validator.loginValidationRule(), validator.validate, authController.login);

routes.post(
  '/forget-password',
  validator.emailValidationRule(),
  validator.validate,
  authController.forgetPassword
);

routes.patch(
  '/reset-password/:id',
  validator.resetPassValidateRule(),
  validator.validate,
  authController.resetPassword
);

routes.get('/reset-password/:resetToken', validator.validate, authController.checkResetToken);

routes.post('/logout', verifyToken, authController.logout);

module.exports = routes;
