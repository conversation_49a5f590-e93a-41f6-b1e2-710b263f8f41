const InventoryHistory = require('../models/inventory-history.model');

/**
 * Create InventoryHistory
 *
 * @param {*} requestData
 * @returns
 */
exports.createInventoryHistory = async (requestData, session) => {
  return await InventoryHistory.create([requestData], session);
};

/**
 * Get InventoryHistory by id
 *
 * @param {*} id
 * @returns
 */
exports.getInventoryHistory = async id => {
  return await InventoryHistory.findById(id);
};

/**
 * Get InventoryHistory by filter
 *
 * @param {*} filter
 * @returns
 */
exports.getInventoryHistoryByFilter = async (filter, page, perPage) => {
  return await InventoryHistory.find(filter)
    .populate([
      {
        path: 'pmOrder',
        select: { orderNumber: 1, _id: 0, status: 1, fromDate: 1, toDate: 1, comments: 1 },
        strictPopulate: false,
      },
      {
        path: 'account',
        select: { name: 1, _id: 0 },
        strictPopulate: false,
      },
      {
        path: 'equipment',
        select: {
          name: 1,
          _id: 1,
          equipmentNumber: 1,
          serialNumber: 1,
        },
        strictPopulate: false,
      },
    ])
    .select({
      createdBy: 0,
      updatedAt: 0,
      __v: 0,
      deletedAt: 0,
      updatedBy: 0,
      deletedBy: 0,
    })
    .limit(parseInt(perPage))
    .skip(parseInt(page) * parseInt(perPage));
};

/**
 * Update InventoryHistory
 *
 * @param {*} id
 * @param {*} requestData
 * @returns
 */
exports.updateInventoryHistory = async (id, requestData) => {
  return await InventoryHistory.findByIdAndUpdate(id, requestData, {
    new: true,
  });
};

exports.getInventoryHistoryOneByFilter = async (filter, sort = 1) => {
  return await InventoryHistory.findOne(filter).sort({ createdAt: sort });
};

exports.prepareInventoryHistoryAndCreate = async (
  equipment,
  status,
  type,
  trackerData,
  orderNumber,
  quantity,
  inOut,
  pmOrder,
  account,
  createdBy,
  session
) => {
  let tracker = await this.inventoryHistoryTracker(type, trackerData);
  const data = {
    equipment,
    status,
    type,
    trackerData,
    orderNumber,
    quantity,
    inOut,
    tracker,
    pmOrder,
    account,
    createdBy,
  };

  const inventoryHistory = session
    ? await this.createInventoryHistory(data, { session })
    : await this.createInventoryHistory(data);
  return inventoryHistory;
};

exports.inventoryHistoryTracker = async (value, trackerData) => {
  let tracker = {
    purchase: trackerData,
    cancel: trackerData,
    order: trackerData,
    'order-received': trackerData,
    return: trackerData,
    'return-received': trackerData,
    'return-rejected': trackerData,
  };

  return tracker[value];
};

exports.getInventoryHistoryDataByFilter = async (filter, sort = 1) => {
  return await InventoryHistory.find(filter).sort({ createdAt: sort });
};

exports.updateInventoryHistoryWithFilter = async (filter, updateData) => {
  return await InventoryHistory.findOneAndUpdate(filter, updateData).sort({ createdAt: -1 });
};
