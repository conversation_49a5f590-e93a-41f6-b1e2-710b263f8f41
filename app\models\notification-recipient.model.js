const { Schema, model } = require('mongoose');

const notificationRecipientSchema = new Schema(
  {
    notification: {
      type: Schema.Types.ObjectId,
      ref: 'notification',
      required: true,
      index: true,
    },
    recipient: {
      type: Schema.Types.ObjectId,
      ref: 'user',
      required: true,
      index: true,
    },
    isRead: {
      type: Boolean,
      default: false,
    },
    readAt: {
      type: Date,
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { timestamps: true }
);

notificationRecipientSchema.index({ recipient: 1, createdAt: -1 }, { background: true });
notificationRecipientSchema.index({ recipient: 1, isRead: 1, createdAt: -1 }, { background: true });
notificationRecipientSchema.index(
  { notification: 1, recipient: 1 },
  { unique: true, background: true }
);

module.exports = model('notification-recipient', notificationRecipientSchema);
