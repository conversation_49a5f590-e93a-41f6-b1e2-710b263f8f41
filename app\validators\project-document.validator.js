const { body, validateParamIds, constantUtils } = require('./parent.validator');

// Define the enum values for the document type
const DocumentTypeEnum = Object.freeze({
  PROCEDURE: 'procedure',
  MANAGE_OF_CHAGE: 'management_of_change',
  QUALITY_ALERT: 'quality_alert',
  SAFETY_ALERT: 'safety_alert',
});

const createProjectDocumentValidationRule = () => {
  return [
    body('title').notEmpty().withMessage(constantUtils.DOCUMENT_TITLE_REQUIRED),
    body('project').notEmpty().withMessage(constantUtils.PROJECT_REQUIRED),
    body('documentNumber').notEmpty().withMessage(constantUtils.DOCUMENT_NUMBER_REQUIRED),
    body('type')
      .notEmpty()
      .withMessage(constantUtils.DOCUMENT_TYPE_REQUIRED)
      .isIn(Object.values(DocumentTypeEnum))
      .withMessage(constantUtils.DOCUMENT_TYPE_INVALID),
    body('document')
      .notEmpty()
      .withMessage(constantUtils.CONTRACTUAL_DOCUMENT_REQUIRED)
      .custom(value => {
        if (typeof value !== 'object' || !value.name || !value.url) {
          throw new Error('Document must be an object with name and url');
        }
        return true;
      }),
  ];
};

const updateProjectDocumentValidationRule = () => {
  return [
    body('title')
      .notEmpty()
      .withMessage(constantUtils.DOCUMENT_TITLE_REQUIRED)
      .optional({ checkFalsy: true }),
    body('project')
      .notEmpty()
      .withMessage(constantUtils.PROJECT_REQUIRED)
      .optional({ checkFalsy: true }),
    body('documentNumber')
      .notEmpty()
      .withMessage(constantUtils.DOCUMENT_NUMBER_REQUIRED)
      .optional({ checkFalsy: true }),
    body('type')
      .notEmpty()
      .withMessage(constantUtils.DOCUMENT_TYPE_REQUIRED)
      .isIn(Object.values(DocumentTypeEnum))
      .withMessage(constantUtils.DOCUMENT_TYPE_INVALID)
      .optional({ checkFalsy: true }),
    body('document')
      .notEmpty()
      .withMessage(constantUtils.CONTRACTUAL_DOCUMENT_REQUIRED)
      .custom(value => {
        if (typeof value !== 'object' || !value.name || !value.url) {
          throw new Error('Document must be an object with name and url');
        }
        return true;
      })
      .optional({ checkFalsy: true }),
  ];
};

module.exports = {
  createProjectDocumentValidationRule,
  updateProjectDocumentValidationRule,
  validateParamIds,
};
