global.constant = {
  EXPORT_SERVICES: ['excel', 'pdf'],
  DATE_FORMAT_FOR_EXPORT: 'yyyy-mm-dd-HHMMss',
  DATE_FORMAT_YYYY_MM_DD: 'yyyy-MM-dd',
  DATE_FORMAT_DD_MM_YYYY_HH_MM: 'dd-MM-yyyy HH:mm',
  DATE_FORMAT_DD_MM_YYYY: 'dd-MM-yyyy',
  HEADER_FONT_STYLE: 'Helvetica-Bold',
  DEFAULT_FONT_STYLE: 'Helvetica',
  PDF_ROW_FONT_COLOR: 'black',
  PDF_HEADER_FONT_COLOR: 'white',
  ODD_COLOR_CODE: '#F7FBFF',
  EVEN_COLOR_CODE: '#FFFFFF',
  DEFAULT_PDF_MARGIN: 20,
  DEFAULT_PDF_SIZE: 'A4',
  DEFAULT_PAGE: 0,
  DEFAULT_PER_PAGE: 10,
  DESC_VAL: -1,
  ASC_VAL: 1,
  PASSWORD_MIN: 6,
  APP_LOGO_PATH: `${process.env.CDN_URL}/logo/reynard_logo_pdf.png`,
  APP_LOGO: `${process.env.CDN_URL}/Mail_Images/reynard_logo_pdf.png`,
  CERTIFICATE_APPROVE_IMAGE: `${process.env.CDN_URL}/Mail_Images/approve-certificate.png`,
  CERTIFICATE_REJECT_IMAGE: `${process.env.CDN_URL}/Mail_Images/reject-certificate.png`,
  FOOTER_PDF_APP_LOGO: `${process.env.CDN_URL}/Mail_Images/reynard-pdf-footer-logo.png`,
  PDF_HEADER_COLOR: '#191a51',
  PDF_TABLE_WIDTH: 546,
  UPLOAD_FILE_LIMIT: 3,
  PASSPORT_FILE_LIMIT: 4,
  GDPR_QUESTION_TITLE: [
    'Grant permission to use information for business purposes',
    'Grant permission to use e-mail for communication',
  ],
  /** use this for default data. eg: project, location, members etc. */
  DEFAULT_DATA_IDENTIFIER: 'default',
  NORMAL_DATA_IDENTIFIER: 'normal',
  DEFAULT_WEIGHT: 30,
  DEFAULT_MANUFACTURER: 'manufacturer',
  DEFAULT_TYPEMM2: 'typemm2',
  DEFAULT_STRING: 'string',
  ADMIN_ROLE: 'admin',
  SUPER_ADMIN_ROLE: 'superadmin',
  PROJECT_MANAGER_ROLE: 'projectManager',
  WAREHOUSE_MANAGER_ROLE: 'warehouseManager',
  ADMIN_ROLE_DESCRIPTION: 'Admin',
  PROJECT_MANAGER_ROLE_DESCRIPTION: 'Project Manager',
  WAREHOUSE_MANAGER_ROLE_DESCRIPTION: 'Warehouse Manager',
  PROJECT_MANAGER_ROLE_WEB: 'web',
  WAREHOUSE_MANAGER_ROLE_BOTH: 'both',
  WAREHOUSE_MANAGER_ROLE_WEB: 'web',
  PROJECT_MANAGER_ROLE_BOTH: 'both',
  ADMIN_ROLE_WEB: 'web',
  BANNED_KEYWORD: /^(?:super[-_\s]?admin|admin)$/i,
  ALPHA_NUMERIC_KEYWORD: /^[a-zA-Z0-9 ]*$/,
  /** End */
  DEFAULT_POST_NUMBER_FORMAT: '00001',
  EQUIPMENT_PERMISSON: 'Equipment',
  USER_MANAGEMENT_PERMISSON: 'User Management',
  WAREHOUSE_PERMISSON: 'Warehouse',
  LOGIN_URL: '/authentication/sign-in',
  DAY_CONVERTOR: 86400000, // (1000 * 60 * 60 * 24)
  RETURN_ORDER_PRE_FIX: 'R',
  ORDER_PRE_FIX: 'O',
  PURCHASE_EQUIPMENT_PRE_FIX: 'P',
  CANCEL_EQUIPMENT_PRE_FIX: 'C',
  SYNC_HASH_PRE_FIX: '#',
  ASSET_PER_LOCATION: 'asset_per_location',
  LOCATION: 'location',
  MULTIPLE_ASSETS: 'multiple_assets',
  UPLOAD_FILE_SIZE: 31457280, // 30MB (ex:- 1MB => 1000000 byts)
  Reynard_PRIMARY_COLOR: '#191A51',
  PDF_DATE_FORMAT: 'dd-mm-yyyy HH:MM',
  CERTIFICATE_EXPIRED_LOGO: `${process.env.CDN_URL}/Mail_Images/certificate-expired.png`,
  CERTIFICATE_ALERT_LOGO: `${process.env.CDN_URL}/Mail_Images/certificate-alert.png`,
  EMAIL_CONSTANTS: {
    supportTeamEmail: process.env.SUPPORT_TEAM_EMAIL,
    logo: `${process.env.CDN_URL}/Mail_Images/reynard_logo_pdf.png`,
    currentYear: new Date().getFullYear(),
    bestRegards: process.env.BEST_REGARDS,
    organizationName: 'Reynard',
  },
  LAST_SLASH: /\/([^/]+)$/,
  NOT_APPLICABLE: 'N/A',
  RIGHT_SYMBOL: '✔',
  LIGHT_GREY_BACKGROUND: {
    type: 'pattern',
    pattern: 'solid',
    fgColor: { argb: 'FFe8eCF4' }, // Light GREY Background Color
  },
  LIGHT_RED_BACKGROUND: {
    type: 'pattern',
    pattern: 'solid',
    fgColor: { argb: 'F5E5E5' }, // Light Red Background Color
  },
  LIGHT_ORANGE_BACKGROUND: {
    type: 'pattern',
    pattern: 'solid',
    fgColor: { argb: 'FFEFE5' }, // Light Orage background color
  },
  LIGHT_YELLOW_BACKGROUND: {
    type: 'pattern',
    pattern: 'solid',
    fgColor: { argb: 'FFFFE0' }, // Light Yellow Background color
  },
  DAY_REMAINING_LESS_THAN_ZERO: 0,
  DAY_REMAINING_LESS_THAN_THIRTY: 30,
  DAY_REMAINING_LESS_THAN_SIXTY: 60,
  DAY_REMAINING_LESS_THAN_NINETY: 90,
  ADDRESS: 'address',
  CONTACT_NUMBER: 'contactNumber',
  DRIVINGLICENCE: 'drivingLicence',
  SEAMANSBOOK: 'seamansBook',
  BIRTHDATE: 'birthDate',
  REPORT_LOCATION: 'Location',
  ASSET: 'Asset',
  FROM: 'From',
  TO: 'To',
  COMPLETION: 'Completion',
  TOTAL_COMPLETION: 'Total Completion',
  ZERO_PERCENTAGE: '0%',
  REPORT: 'Report',
  DEFAULT_TIMEZONE: 'Europe/London',
  ESCAPE_CHARACTERS: /[^a-zA-Z0-9]/g,
  KIN_CONTACT_NUMBER: 'kinContactNumber',
  PASSPORT: 'passport',
  PASSPORT_ISSUE_DATE: 'passportIssueDate',
  PASSPORT_EXPIRY_DATE: 'passportExpiryDate',
  SECONDARY_PASSPORT: 'secondaryPassport',
  SECONDARY_PASSPORT_ISSUE_DATE: 'secondaryPassportIssueDate',
  SECONDARY_PASSPORT_EXPIRY_DATE: 'secondaryPassportExpiryDate',
  HEALTH_INSURANCE: 'healthInsurance',
  HEALTH_INSURANCE_ISSUE_DATE: 'healthInsuranceIssueDate',
  HEALTH_INSURANCE_EXPIRY_DATE: 'healthInsuranceExpiryDate',
  LIABILITY_INSURANCE: 'liabilityInsurance',
  LIABILITY_INSURANCE_ISSUE_DATE: 'liabilityInsuranceIssueDate',
  LIABILITY_INSURANCE_EXPIRY_DATE: 'liabilityInsuranceExpiryDate',
  MOBILE_USER_AGENTS: ['Android', 'iPhone'],
  REPORTING_PERIOD: '0:00 - 24:00',
  APPROVED_STATUS: 'approved',
  INITIAL_DPR_NUMBER: '001',
  CARD_STATUS: ['submitted', 'submitted to client', 'in_discussion'],
  DEFAULT_EQUIPMENT_CURRENT_LOCATION: 'in_warehouse',
  TODAY: 'today',
  WEEK: 'week',
  MONTH: 'month',
  ALL: 'all',
  CARD_TYPE: [
    'unsafe',
    'safe',
    'ncr',
    'incident',
    'Near Miss',
    'Injury / Illness',
    'Damage / Breakdown / Loss',
    'Spill / Pollution / Emission',
    'Breach of law / Contract',
    'Other non-work events',
  ],
  MILLISECOND_TO_MINUTES: 60000,
  CONVERT_TO_HOURS: 60,
  PAYLOAD_SIZE_LIMIT: '50mb',
  TEMP_EQUIPMENT_TYPE_PRE_FIX: 'TMP',
  OTHER_PROJECT: 'Other_Project',
  PROJECT_DOCUMENT_TYPES: ['procedure', 'management_of_change', 'quality_alert', 'safety_alert'],
  PROJECT_DOCUMENT_TYPES_ENUM: {
    PROCEDURE: 'procedure',
    MANAGEMENT_OF_CHANGE: 'management_of_change',
    QUALITY_ALERT: 'quality_alert',
    SAFETY_ALERT: 'safety_alert',
  },
  EXCEL_TIME_DIVISOR: {
    HOURS: 24,
    MINUTES: 1440,
    MINUTES_VALUE: 60,
    ROUND_OFF_VALUE: 100,
  },
  QHSE_TITLE_LIMIT: 25,
  QHSE_CATEGORY_LIMIT: 25,
  START_OF_DAY_HOURS: [0, 0, 0, 0],
  END_OF_DAY_HOURS: [23, 59, 59, 999],
  RISK_OF_INCIDENT: [
    { min: 1, max: 4, risk: 'Low' },
    { min: 5, max: 12, risk: 'Medium' },
    { min: 15, max: 25, risk: 'High' },
  ],
  UNKNOWN: 'Unknown',
  BINARY_CONVERSION_FACTOR: 1024,
  WEB: 'web',
  MOBILE: 'mobile',
  BOTH: 'both',
  NAME_REGEX_PERSONNELS: /^[A-Za-zÀ-ÿ\s'-]+$/,
  DAYS_OF_MONTH: 30,
  CERTIFICATE_EXPIRY: [
    { days: -15, label: 'expired 15 days ago' },
    { days: 0, label: 'expires today' },
    { days: 15, label: 'expires in 15 days' },
    { days: 30, label: 'expires in 30 days' },
    { days: 60, label: 'expires in 60 days' },
  ],
  NOTIFICATION_DETAILS: {
    module: {
      certificateApproval: 'certificate_approval',
    },
    priority: {
      low: 'low',
      medium: 'medium',
      high: 'high',
    },
  },
};
