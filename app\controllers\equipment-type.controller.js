// Services
const equipmentTypeService = require('../services/equipment-type.service');
const equipmentQuantityTypeService = require('../services/equipment-quantity-type.service');
const equipmentService = require('../services/equipment.service');

// Utils
const constantUtils = require('../utils/constants.utils');
const responseUtils = require('../utils/response.utils');
const commonUtils = require('../utils/common.utils');
const { validateSearch, updateSyncApiManage } = require('../utils/common-function.utils');

/**
 * Create EquipmentType
 *
 * @param {*} req
 * @param {*} res
 */
exports.createEquipmentType = async (req, res) => {
  try {
    const requestData = req.body;
    const response = await equipmentTypeService.createEquipmentType(requestData);
    if (response) {
      await this.commonUpdateSyncApiManage(req.userData.account);
    }
    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.CREATE_EQUIPMENT_TYPE, response));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Get EquipmentCategories
 *
 * @param {*} req
 * @param {*} res
 */
exports.getEquipmentType = async (req, res) => {
  try {
    let { account } = req.userData;
    let page = req.query.page ? parseInt(req.query.page) : '';
    let perPage = req.query.perPage ? parseInt(req.query.perPage) : '';
    let sort;

    if (!req.query?.sortOrder) {
      sort = req.query.sort && req.query.sort === 'asc' ? { createdAt: 1 } : { createdAt: -1 };
    } else {
      sort = req.query.sortOrder === 'asc' ? { type: 1 } : { type: -1 };
    }

    let filterData = {
      account: account,
      deletedAt: null,
      ...(req.query.name && { type: await commonUtils.convertToCaseInsensetive(req.query.name) }),
    };

    // Get total count
    const allRecordsCount = await equipmentTypeService.countEquipmentType(filterData);
    // Get paginated data
    const equipmentTypeData = await equipmentTypeService.getEquipmentType(
      filterData,
      page,
      perPage,
      sort
    );
    const currentPage = page;

    const responseObj = { allRecordsCount, currentPage, equipmentTypeData };
    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.GET_EQUIPMENT_TYPE, responseObj));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Get EquipmentType by Id
 *
 * @param {*} req
 * @param {*} res
 */
exports.getEquipmentTypeById = async (req, res) => {
  try {
    let { id } = req.params;
    const response = await equipmentTypeService.getEquipmentTypeById(id);
    if (!response) {
      return res
        .status(404)
        .json(responseUtils.errorResponse(constantUtils.EQUIPMENT_TYPE_NOT_FOUND));
    }
    res.status(200).json(responseUtils.successResponse(constantUtils.GET_EQUIPMENT_TYPE, response));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Update EquipmentType
 *
 * @param {*} req
 * @param {*} res
 */
exports.updateEquipmentType = async (req, res) => {
  try {
    let { id } = req.params;
    const requestData = req.body;
    const response = await equipmentTypeService.getEquipmentTypeById(id);
    if (!response) {
      return res
        .status(404)
        .json(responseUtils.errorResponse(constantUtils.EQUIPMENT_TYPE_NOT_FOUND));
    }

    const responseUpdate = await equipmentTypeService.updateEquipmentType(id, requestData);

    // check and update equipment price on buy price type
    const getEquipmentQuantityType =
      await equipmentQuantityTypeService.getEquipmentQuantityTypeById(requestData.quantityType);

    if (
      responseUpdate &&
      requestData.price != response.price &&
      getEquipmentQuantityType.priceType === 'buy'
    ) {
      const getEquipment = await equipmentService.getEquipments({
        equipmentType: id,
        deletedAt: null,
      });

      if (getEquipment.length > 0) {
        Object.keys(getEquipment).forEach(key => {
          equipmentService.updateEquipment(getEquipment[key]._id, {
            value: responseUpdate.price,
          });
        });
      }
    }
    if (responseUpdate) {
      await this.commonUpdateSyncApiManage(req.userData.account);
    }
    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.UPDATE_EQUIPMENT_TYPE, responseUpdate));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Delete EquipmentType
 *
 * @param {*} req
 * @param {*} res
 */
exports.deleteEquipmentType = async (req, res) => {
  try {
    let { id } = req.params;
    const response = await equipmentTypeService.getEquipmentTypeById(id);
    if (!response) {
      return res
        .status(404)
        .json(responseUtils.errorResponse(constantUtils.EQUIPMENT_TYPE_NOT_FOUND));
    }

    const getEquipmentByEquipmentTypeId = await equipmentService.getSingleEquipmentByFilter({
      equipmentType: id,
    });

    // check if equipment type has equipment
    if (getEquipmentByEquipmentTypeId) {
      return res
        .status(405)
        .json(responseUtils.errorResponse(constantUtils.CANNOT_DELETE_EQUIPMENT_TYPE));
    }

    const responseDelete = await equipmentTypeService.deleteEquipmentType(id, req.deletedAt);
    if (responseDelete) {
      await this.commonUpdateSyncApiManage(req.userData.account);
    }
    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.DELETE_EQUIPMENT_TYPE, responseDelete));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * check unique quantity type has been used
 *
 * @param {*} requestData
 * @returns
 */
exports.validateQuantityType = async (requestData, equipmentTypeId = null) => {
  const { quantityType, account } = requestData;

  const equipmentTypeData =
    equipmentTypeId !== null
      ? await equipmentTypeService.getEquipmentTypeById(equipmentTypeId)
      : null;

  if (
    equipmentTypeData !== null &&
    equipmentTypeData.quantityType._id.toString() === quantityType.toString()
  ) {
    return true;
  }

  const quantityTypeData = await equipmentQuantityTypeService.getEquipmentQuantityTypeById(
    quantityType
  );

  if (quantityTypeData?.quantityType === 'unique') {
    const equipmentTypeData = await equipmentTypeService.getEquipmentType(
      {
        quantityType,
        account,
        deletedAt: null,
      },
      '',
      '',
      -1
    );

    if (equipmentTypeData.length > 0) {
      throw new Error(constantUtils.EQUIPMENT_TYPE_FOUND);
    }
  }
  return true;
};

/**
 * Get Equipment Types
 *
 * @param {*} req
 * @param {*} res
 */
exports.getEquipmentTypes = async (req, res) => {
  try {
    let { account } = req.userData;
    let page = req.query.page ? req.query.page : '';
    let perPage = req.query.perPage ? req.query.perPage : '';
    let sort = req.query.sort && req.query.sort === 'asc' ? { createdAt: 1 } : { createdAt: -1 };
    let search = await validateSearch(req.query.search);

    if (!search && search !== '') {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.INVALID_SEARCH));
    }

    if (req.query?.sortName) {
      sort = req.query.sortName === 'asc' ? { type: 1 } : { type: -1 };
    }

    let filter = {
      account: account,
      isActive: true,
      deletedAt: null,
    };

    // filter by equipment category
    if (req.query?.equipmentCategory) {
      let equipmentCategoryIds = req.query.equipmentCategory.split(',');
      equipmentCategoryIds = equipmentCategoryIds.map(id => commonUtils.toObjectId(id));
      filter.equipmentCategory = { $in: equipmentCategoryIds };
    }

    const response = await equipmentTypeService.getEquipmentTypes(
      filter,
      page,
      perPage,
      sort,
      search
    );

    res.status(200).json(responseUtils.successResponse(constantUtils.GET_EQUIPMENT_TYPE, response));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

exports.commonUpdateSyncApiManage = async account => {
  await updateSyncApiManage({
    syncApis: ['equipmentConfig'],
    account,
  });
};
