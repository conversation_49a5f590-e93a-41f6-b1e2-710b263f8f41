// Services
const dprService = require('../services/dpr.service');
const projectService = require('../services/project.service');
const memberService = require('../services/member.service');
const { exportDprPdf } = require('../services/pdf-template.service');

// Utils
const constantUtils = require('../utils/constants.utils');
const responseUtils = require('../utils/response.utils');
const HTTP_STATUS = require('../utils/status-codes');
const commonUtils = require('../utils/common.utils');
const exportExcelUtils = require('../utils/export-excel.util');

/**
 * Create DPR
 *
 * @param {*} req
 * @param {*} res
 */
exports.createDpr = async (req, res) => {
  try {
    const reqData = req.body;
    const { account, _id } = req.userData;
    reqData.account = account;
    reqData.createdBy = _id;

    const dprList = await dprService.getAllDpr({
      project: reqData.project,
      account,
      deletedAt: null,
    });

    // Normalize the dates to match format
    const normalizedReqDate = reqData.dprDate.split('T')[0];

    const isDprExist = dprList.some(dpr => {
      const normalizedDprDate = new Date(dpr.dprDate).toISOString().split('T')[0];
      return normalizedDprDate === normalizedReqDate;
    });

    if (isDprExist) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(responseUtils.errorResponse(constantUtils.DPR_ALREADY_EXIST));
    }

    if (dprList.length === 0) {
      reqData.dprNo = global.constant.INITIAL_DPR_NUMBER;
    } else {
      reqData.dprNo = String(parseInt(dprList[0]?.dprNo) + 1).padStart(3, '0');
    }

    // Append 'Z' to the DPR date to indicate UTC timezone
    reqData.dprDate = reqData.dprDate + 'Z';

    const createdDpr = await dprService.createDpr(reqData);
    res
      .status(HTTP_STATUS.CREATED)
      .json(responseUtils.successResponse(constantUtils.DPR_CREATE_SUCCESS, createdDpr));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(responseUtils.errorResponse(error));
  }
};

/**
 * Update DPR
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.updateDpr = async (req, res) => {
  try {
    const { dprId } = req.params;
    const reqData = req.body;
    const { account, _id } = req.userData;
    reqData.account = account;
    reqData.updatedBy = _id;

    const dpr = await dprService.getLatestDpr({
      _id: dprId,
      account: reqData.account,
      deletedAt: null,
    });

    if (!dpr) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(responseUtils.errorResponse(constantUtils.NO_DPR));
    }

    const existingDprData = dpr.dprData.find(item => item.version === reqData.version);

    let updateQuery;
    let updateAction;

    if (existingDprData) {
      const { version, dprData, ...otherFields } = reqData;

      const dprDataFields = Object.entries(dprData).reduce((fields, [key, value]) => {
        fields[`dprData.$.${key}`] = value;
        return fields;
      }, {});

      updateQuery = { _id: dprId, 'dprData.version': version };
      updateAction = { $set: { ...dprDataFields, ...otherFields } };
    } else {
      const { dprData, ...otherFields } = reqData;

      updateQuery = { _id: dprId };
      updateAction = {
        $push: { dprData: { ...dprData } },
        $set: otherFields,
      };
    }

    const updatedDpr = await dprService.updateDpr(updateQuery, updateAction);

    return res
      .status(HTTP_STATUS.OK)
      .json(responseUtils.successResponse(constantUtils.DPR_UPDATE_SUCCESS, updatedDpr));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(responseUtils.errorResponse(error));
  }
};

/**
 * Delete DPR
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.deleteDpr = async (req, res) => {
  try {
    const { dprId } = req.params;
    const { account } = req.userData;
    const dpr = await dprService.getLatestDpr({
      _id: dprId,
      account: account,
      deletedAt: null,
    });

    if (!dpr) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(responseUtils.errorResponse(constantUtils.NO_DPR));
    }

    const deletedDpr = await dprService.deleteDpr(dprId, req.deletedAt);
    res
      .status(HTTP_STATUS.OK)
      .json(responseUtils.successResponse(constantUtils.DPR_DELETE_SUCCESS, deletedDpr));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(responseUtils.errorResponse(error));
  }
};

/**
 * Hard Delete DPR
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.hardDeleteDpr = async (req, res) => {
  try {
    const { dprId } = req.params;
    const { account } = req.userData;

    const isValidDprId = commonUtils.isValidId(dprId);
    if (!isValidDprId) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(responseUtils.errorResponse(constantUtils.NO_DPR));
    }

    const dpr = await dprService.getLatestDpr({
      _id: dprId,
      account,
      deletedAt: null,
    });

    if (!dpr) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(responseUtils.errorResponse(constantUtils.NO_DPR));
    }

    const dprDeleted = await dprService.hardDeleteDpr(dprId);

    res
      .status(HTTP_STATUS.OK)
      .json(responseUtils.successResponse(constantUtils.DPR_DELETE_SUCCESS, dprDeleted));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(responseUtils.errorResponse(error));
  }
};

/**
 * Get All DPRs
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.getAllDprs = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 0;
    const perPage = parseInt(req.query.perPage) || 10;
    const sortBy = req.query.sortBy || 'dprNo';
    const sortOrder = req.query.sortOrder || 'desc';
    const { project, status, date, projectStatus, fromDate, toDate } = req.query;

    let filterData = {
      account: req.userData.account,
      deletedAt: null,
    };

    if (project && project !== 'all') filterData.project = project;
    if (status && status !== 'all') filterData.status = status;

    // Add project filter if project status is provided
    if (projectStatus && !project) {
      filterData = await commonUtils.filterProjectStatus(
        projectStatus,
        req.userData.account,
        filterData,
        req?.assignedProjectList
      );
    }

    delete filterData?.projectStatus;

    if (date && date !== 'all') {
      filterData = await commonUtils.getCreatedDateFilter({
        ...filterData,
        created: date,
        fromDate,
        toDate,
      });
    }

    const { dprs, total } = await dprService.getListOfAllDprs(
      filterData,
      page,
      perPage,
      sortBy,
      sortOrder
    );

    const responseData = {
      dprs,
      currentPage: page,
      perPage,
      totalPages: Math.ceil(total / perPage),
      totalRecords: total,
    };

    return res.status(HTTP_STATUS.OK).json({
      success: true,
      data: responseData,
    });
  } catch (error) {
    return res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(responseUtils.errorResponse(error));
  }
};

/**
 * Get DPR Members
 *
 * @param {*} req
 * @param {*} res
 */
exports.getDprMembers = async (req, res) => {
  try {
    const { dprId } = req.params;

    const dpr = await dprService.getLatestDpr({
      _id: dprId,
      account: req.userData.account,
      deletedAt: null,
    });

    if (!dpr) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(responseUtils.errorResponse(constantUtils.NO_DPR));
    }

    const exist = await projectService.getProjectById(dpr.project, req.userData.account);

    if (!exist) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.NO_PROJECT));
    }

    let dprMembers = await memberService.getAllMember({
      project: dpr.project,
      account: req.userData.account,
      showOnDpr: true,
      deletedAt: null,
    });

    dprMembers = dprMembers.map(item => {
      return {
        name: `${item.user.callingName || item.user.firstName || ''} ${
          item.user.lastName || ''
        }`.trim(),
        function: item.function?.functionName || global.constant.NA,
        email: item.user?.email || global.constant.NO_EMAIL,
        phone:
          `${item.user?.contactNumber?.in} ${item.user?.contactNumber?.number}` ||
          global.constant.NO_CONTACT_NUMBER,
      };
    });

    const response = {
      reportingPeriod: global.constant.REPORTING_PERIOD,
      date: dpr.dprDate,
      client: exist.client,
      project: `${exist.projectNumber} - ${exist.title}`,
      dprNumber: dpr.dprNo,
      version: dpr.version,
      status: dpr.status,
      dprMembers,
    };

    res.status(200).json(responseUtils.successResponse(constantUtils.DPR_FETCH, response));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Get DPR Details
 *
 * @param {*} req
 * @param {*} res
 */
exports.getDprDetails = async (req, res) => {
  try {
    const { dprId } = req.params;
    const { account } = req.userData;

    const dpr = await dprService.getDprDetails({
      _id: dprId,
      account: account,
      deletedAt: null,
    });

    if (!dpr) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(responseUtils.errorResponse(constantUtils.NO_DPR));
    }

    const exist = await projectService.getProjectById(dpr.project, req.userData.account);

    if (!exist) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.NO_PROJECT));
    }

    const currentVersionData = dpr.dprData.find(item => item.version === dpr.version);

    const response = {
      _id: dpr._id,
      currentVersionData,
      version: dpr.version,
      prevVersion: dpr.prevVersion,
      dprNo: dpr.dprNo,
      dprDate: dpr.dprDate,
      client: exist.client,
      project: `${exist.projectNumber} - ${exist.title}`,
      status: dpr.status,
      lastReload: dpr.lastReload,
      lastReloadTime: dpr.lastReloadTime,
      createdAt: dpr.createdAt,
      updatedAt: dpr.updatedAt,
      createdBy: dpr.createdBy,
      updatedBy: dpr.updatedBy,
      deletedAt: dpr.deletedAt,
      allDprVersionData: dpr.dprData,
    };

    return res
      .status(HTTP_STATUS.OK)
      .json(responseUtils.successResponse(constantUtils.DPR_FETCH, response));
  } catch (error) {
    return res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(responseUtils.errorResponse(error));
  }
};

/**
 * Get DPR Excel
 *
 * @param {*} req
 * @param {*} res
 */
exports.getDprExcel = async (req, res) => {
  try {
    let filterData = { account: req.userData.account, deletedAt: null };

    let removeKeys = ['all'];
    const queryParams = await commonUtils.filterParamsModify(req.query, removeKeys);

    if (queryParams.project) filterData.project = queryParams.project;
    if (queryParams.status) filterData.status = queryParams.status;

    if (queryParams.date) {
      filterData = await commonUtils.getCreatedDateFilter({
        ...filterData,
        created: queryParams.date,
        fromDate: queryParams.fromDate,
        toDate: queryParams.toDate,
      });
    }

    const data = await dprService.getListOfAllDprs(filterData, null, null, 'createdAt', 'desc');
    await exportExcelUtils.exportDprExcel(res, data);
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Export DPR pdf
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.exportDprPdf = async (req, res) => {
  try {
    const { dprId, dprVersion } = req.params;

    const dpr = await dprService.getDprDetails({
      _id: dprId,
      account: req.userData.account,
      deletedAt: null,
    });

    if (!dpr) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(responseUtils.errorResponse(constantUtils.NO_DPR));
    }

    const dprData = dprService.getDprDataByDprVersion(dpr.dprData, dprVersion);

    if (!dprData) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(responseUtils.errorResponse(constantUtils.NO_DPR_DATA));
    }
    return await exportDprPdf(dprData, res);
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(responseUtils.errorResponse(error));
  }
};

/**
 * Reload DPR
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.updateReload = async (req, res) => {
  try {
    const { dprId } = req.params;
    const { lastReload, lastReloadTime } = req.body;

    const dpr = await dprService.getLatestDpr({
      _id: dprId,
      account: req.userData.account,
      deletedAt: null,
    });

    if (!dpr) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(responseUtils.errorResponse(constantUtils.NO_DPR));
    }

    const updatedDprData = await dprService.updateDprReloadStatus(dprId, {
      lastReload,
      lastReloadTime,
    });

    return res
      .status(HTTP_STATUS.OK)
      .json(responseUtils.successResponse(constantUtils.DPR_RELOAD_SUCCESS, updatedDprData));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(responseUtils.errorResponse(error));
  }
};
