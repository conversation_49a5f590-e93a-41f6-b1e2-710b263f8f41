// library
const express = require('express');
const routes = express.Router();

// middleware
const { verifyToken, deletedAt, authAccount } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');

// controller
const scopeController = require('../controllers/scope.controller');

// Validator
const validator = require('../validators/scope.validator');

// create scope
routes.post(
  '',
  verifyToken,
  authAccount,
  validator.scopeValidationRule(),
  validate,
  scopeController.createScope
);

// update scope
routes.patch('/:id', verifyToken, authAccount, validate, scopeController.updateScope);

// delete scope
routes.delete('/:id', verifyToken, authAccount, deletedAt, scopeController.deleteScope);

routes.get(
  '/projects/:projectId/locations/:locationId',
  verifyToken,
  authAccount,
  scopeController.getScopeWithActivityDetails
);

module.exports = routes;
