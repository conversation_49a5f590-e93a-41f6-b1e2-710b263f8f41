const { Schema, model } = require('mongoose');

const notificationSchema = new Schema(
  {
    // Basic notification info
    title: {
      type: String,
      required: true,
      maxlength: 100,
    },
    body: {
      type: String,
      maxlength: 500,
      default: null,
    },

    // Notification module and priority
    module: {
      type: String,
      enum: [
        'shift',
        'toolbox_talk',
        'report',
        'project_tracker',
        'dpr',
        'qhse',
        'feedback',
        'inventory',
        'orders',
        'project_inventory',
        'project_orders',
        'return_cart',
        'personnel',
        'certificate_approval',
        'training_matrix',
        'settings',
        'custom',
      ],
      required: true,
      index: true,
    },
    priority: {
      type: String,
      enum: ['low', 'medium', 'high'],
      default: 'medium',
      index: true,
    },

    // Deep-link url as per module
    actionUrl: { type: String, default: null },

    // Metadata
    account: {
      type: Schema.Types.ObjectId,
      ref: 'account',
      required: true,
      index: true,
    },

    // Sender info
    sender: {
      type: Schema.Types.ObjectId,
      ref: 'user',
      required: true,
      index: true,
    },

    // For soft delete
    deletedAt: {
      type: Date,
      default: null,
    },

    // Audit fields
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: 'user',
      required: true,
    },
    updatedBy: {
      type: Schema.Types.ObjectId,
      ref: 'user',
      default: null,
    },
  },
  { timestamps: true }
);

notificationSchema.index({ account: 1, createdAt: -1 });
notificationSchema.index({ module: 1, createdAt: -1 });

module.exports = model('notification', notificationSchema);
