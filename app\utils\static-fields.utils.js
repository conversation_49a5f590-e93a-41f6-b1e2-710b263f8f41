// Services
const projectServices = require('../services/project.service');
const locationServices = require('../services/location.service');
const severityServices = require('../services/severity.service');
const likelihoodServices = require('../services/likelihood.service');
const categoryServices = require('../services/category.service');
const typeServices = require('../services/type.service');
const teamServices = require('../services/team.service');
const memberServices = require('../services/member.service');
const assetServices = require('../services/asset.service');
const reportTypeServices = require('../services/report-type.service');
const activityServices = require('../services/activity.service');
const equipmentCategoryServices = require('../services/equipment-category.service');
const equipmentTypeServices = require('../services/equipment-type.service');
const warehouseService = require('../services/warehouse.service');
const equipmentUnitServices = require('../services/equipment-unit.service');
const quantityTypeServices = require('../services/equipment-quantity-type.service');
const certificateTypeServices = require('../services/equipment-certificate-type.service');
const hsCodeServices = require('../services/hs-code.service');
const manageReportService = require('../services/manage-report.service');
const { toObjectId } = require('../utils/common.utils');

// Json Format
// Note: Static id change required change in mobile database.
const { properties, option } = require('./json-format.utils');
const { feedback } = require('./config-constants.utils');

// utils
const commonUtils = require('../utils/common.utils');

const imageUrl = `${process.env.IMAGE_URL}/icons`;

exports.projectStatus = ''; // Project status for mobile

/**
 * Set projectStatus based on user agent
 *
 * @param {*} req
 */
const constructFunction = req => {
  const isMobileUser = global.constant.MOBILE_USER_AGENTS.includes(req.headers['user-agent']);
  if (isMobileUser) {
    this.projectStatus = 'open';
  } else if (req.query?.projectStatus) {
    this.projectStatus = { $in: req.query.projectStatus.split(',') };
  } else {
    this.projectStatus = '';
  }
};

const GetSafeCardStaticFile = async (user, status = true) => {
  let filterData = {
    account: user.account,
    ...(status && { isActive: true }),
    deletedAt: null,
    isDefault: false,
  };

  filterData = await this.getAndAddAssignProjectList(filterData, user);

  const projectList = await projectServices.getAllProjects({
    ...filterData,
    ...(this.projectStatus && { status: this.projectStatus }),
  });
  // default project
  const defaultProjectList = await projectServices.getDefaultProjects(user.account);

  let filterLocation = {
    account: user.account,
    deletedAt: null,
  };
  filterLocation = await this.getAndAddAssignProjectList(filterLocation, user, 'location');
  const locationsList = await locationServices.getAllLocationByAccountId(filterLocation);

  let filterCategory = {
    account: toObjectId(user.account),
    isVisibleForSafeCard: true,
    deletedAt: null,
  };

  const categoryList = await categoryServices.getAllCategory(filterCategory);
  // Title field
  let titleProperties = { ...properties };
  titleProperties.title = 'Title';
  titleProperties.type = 'text';
  titleProperties.id = 'title';
  titleProperties.fieldSortOrder = 0;
  titleProperties.iconUrl = `${imageUrl}/pencil-sharp.png`;
  titleProperties.hint = 'Please Enter Title';
  titleProperties.IsRequired = true;
  titleProperties.dependentFieldIds =
    titleProperties.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ')
      ? ['defaultProject']
      : [];

  // Project properties
  let projectProperties = { ...properties };
  projectProperties.title = 'Project';
  projectProperties.type = 'options';
  projectProperties.hint = 'Select';
  projectProperties.id = 'project';
  // default project
  projectProperties.hasChildField = true;
  projectProperties.fieldSortOrder = 0;
  projectProperties.iconUrl = `${imageUrl}/ic_form_project.png`;
  projectProperties.options = projectList.map(item => {
    let projectOption = { ...option };

    projectOption.title =
      item?.projectNumber !== '' ? `${item.projectNumber} - ${item.title}` : item?.title;
    projectOption.id = item?._id;
    projectOption.isDefault =
      projectOption.title === process.env.DEFAULT_PROJECT.replace(/_/g, ' ');
    // default project
    projectOption.dependentFieldIds =
      projectOption.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ')
        ? ['defaultProject']
        : [];
    return projectOption;
  });
  projectProperties.IsRequired = true;

  // Default project field
  let defaultProjectNameProperties = { ...properties };
  defaultProjectNameProperties.title = process.env.DEFAULT_PROJECT.replace(/_/g, ' ');
  defaultProjectNameProperties.type = 'options';
  defaultProjectNameProperties.IsRequired = false;
  defaultProjectNameProperties.isDefaultVisible = false;
  defaultProjectNameProperties.id = 'defaultProject';
  defaultProjectNameProperties.fieldSortOrder = 0;
  defaultProjectNameProperties.iconUrl = `${imageUrl}/ic_form_project.png`;
  defaultProjectNameProperties.hint = process.env.DEFAULT_PROJECT.replace(/_/g, ' ');
  defaultProjectNameProperties.parentFieldId = 'project';
  defaultProjectNameProperties.options = defaultProjectList.map(item => {
    let tempOption = { ...option };
    tempOption.title = item.title;
    tempOption.isVisibleForOptions = [];
    tempOption.id = item?._id;
    tempOption.isDefault = true;
    tempOption.dependentFieldIds =
      tempOption.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ') ? ['defaultProject'] : [];
    return tempOption;
  });

  // Location properties
  let locationProperties = { ...properties };
  locationProperties.title = 'Location';
  if (locationsList.length > 0) {
    locationProperties.type = 'options';
    locationProperties.hint = 'Select';
  } else {
    locationProperties.type = 'text';
    locationProperties.hint = 'Please Enter Location';
  }
  locationProperties.id = 'location';
  locationProperties.fieldSortOrder = 0;
  locationProperties.iconUrl = `${imageUrl}/ic_form_location.png`;
  locationProperties.options = locationsList.map(item => {
    let tempOption = { ...option };
    tempOption.title = item.title;
    tempOption.isVisibleForOptions = [item?.project?._id];
    tempOption.id = item?._id;
    tempOption.isDefault = item?.isDefault || false;
    tempOption.dependentFieldIds =
      tempOption.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ') ? ['defaultProject'] : [];
    return tempOption;
  });
  locationProperties.parentFieldId = 'project';
  locationProperties.IsRequired = true;

  // Category properties
  let categoryProperties = { ...properties };
  categoryProperties.title = 'Category';
  categoryProperties.type = 'options';
  categoryProperties.hint = 'Select';
  categoryProperties.id = 'category';
  categoryProperties.fieldSortOrder = 0;
  categoryProperties.iconUrl = `${imageUrl}/ic_categories.png`;
  categoryProperties.options = categoryList.map(item => {
    let tempOption = { ...option };
    tempOption.title = item?.categoryName;
    tempOption.iconUrl = item?.iconUrl;
    tempOption.id = item?._id;
    tempOption.dependentFieldIds =
      tempOption.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ') ? ['defaultProject'] : [];
    return tempOption;
  });
  categoryProperties.IsRequired = true;

  // Description field
  let descriptionProperties = { ...properties };
  descriptionProperties.title = 'Description';
  descriptionProperties.type = 'textarea';
  descriptionProperties.fieldSortOrder = 0;
  descriptionProperties.id = 'description';
  descriptionProperties.iconUrl = `${imageUrl}/pencil-sharp.png`;
  descriptionProperties.hint = 'Please Enter Description';
  descriptionProperties.dependentFieldIds =
    descriptionProperties.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ')
      ? ['defaultProject']
      : [];

  // Action / Recommendation field
  let ActionRecomendationProperties = { ...properties };
  ActionRecomendationProperties.title = 'Actions / Recommendations';
  ActionRecomendationProperties.type = 'textarea';
  ActionRecomendationProperties.fieldSortOrder = 0;
  ActionRecomendationProperties.id = 'actionsTaken';
  ActionRecomendationProperties.iconUrl = `${imageUrl}/pencil-sharp.png`;
  ActionRecomendationProperties.hint = 'Please Enter Actions / Recommendations';
  ActionRecomendationProperties.dependentFieldIds =
    ActionRecomendationProperties.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ')
      ? ['defaultProject']
      : [];
  // Photo field
  let photoProperties = { ...properties };
  photoProperties.title = 'Images';
  photoProperties.type = 'images';
  photoProperties.fieldSortOrder = 0;
  photoProperties.id = 'images';
  photoProperties.iconUrl = `${imageUrl}/pencil-sharp.png`;
  photoProperties.selectedOption = [];
  photoProperties.dependentFieldIds =
    photoProperties.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ') ? ['otherProject'] : [];

  let SafeFields = [
    titleProperties,
    projectProperties,
    defaultProjectNameProperties, // default project field
    locationProperties,
    categoryProperties,
    descriptionProperties,
    ActionRecomendationProperties,
    photoProperties,
  ];

  // Status update, status field are only accessible for web user
  if (!user.isMobile) {
    // status field
    let StatusProperties = { ...properties };
    StatusProperties.title = 'Status';
    StatusProperties.type = 'options';
    StatusProperties.id = 'status';
    StatusProperties.fieldSortOrder = 0;
    StatusProperties.iconUrl = `${imageUrl}/pencil-sharp.png`;
    let opt = ['Open', 'Submitted', 'In Discussion', 'Closed', 'Archived'];
    StatusProperties.options = opt.map(item => {
      let tempOption = { ...option };
      tempOption.title = item;
      tempOption.iconUrl = item;
      tempOption.id = item.replace(/\s+/g, '_').toLowerCase();
      tempOption.dependentFieldIds =
        tempOption.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ')
          ? ['defaultProject']
          : [];
      return tempOption;
    });
    SafeFields.push(StatusProperties);
  }

  return SafeFields;
};

const GetUnSafeCardStaticFile = async (user, status = true) => {
  let filterData = {
    account: user.account,
    ...(status && { isActive: true }),
    deletedAt: null,
    isDefault: false,
  };

  filterData = await this.getAndAddAssignProjectList(filterData, user);

  const projectList = await projectServices.getAllProjects({
    ...filterData,
    ...(this.projectStatus && { status: this.projectStatus }),
  });

  let filterLocation = {
    account: user.account,
    deletedAt: null,
  };
  filterLocation = await this.getAndAddAssignProjectList(filterLocation, user, 'location');
  const locationsList = await locationServices.getAllLocationByAccountId(filterLocation);
  const severityList = await severityServices.getAllSeverity();
  const likelihoodList = await likelihoodServices.getAllLikelihood();
  // default project field
  const defaultProjectList = await projectServices.getDefaultProjects(user.account);

  let filterCategory = {
    account: toObjectId(user.account),
    isVisibleForUnsafeCard: true,
    deletedAt: null,
  };
  const categoryList = await categoryServices.getAllCategory(filterCategory);
  // Title field
  let titleProperties = { ...properties };
  titleProperties.title = 'Title';
  titleProperties.type = 'text';
  titleProperties.id = 'title';
  titleProperties.fieldSortOrder = 0;
  titleProperties.iconUrl = `${imageUrl}/pencil-sharp.png`;
  titleProperties.hint = 'Please Enter Title';
  titleProperties.IsRequired = true;
  titleProperties.dependentFieldIds =
    titleProperties.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ')
      ? ['defaultProject']
      : [];
  // Project properties
  let projectProperties = { ...properties };
  projectProperties.title = 'Project';
  projectProperties.type = 'options';
  projectProperties.hint = 'Select';
  projectProperties.id = 'project';
  // default project
  projectProperties.hasChildField = true;
  projectProperties.fieldSortOrder = 0;
  projectProperties.iconUrl = `${imageUrl}/ic_form_project.png`;
  projectProperties.options = projectList.map(item => {
    let projectOption = { ...option };
    projectOption.title =
      item?.projectNumber !== '' ? `${item.projectNumber} - ${item.title}` : item?.title;
    projectOption.id = item?._id;
    projectOption.isDefault =
      projectOption.title === process.env.DEFAULT_PROJECT.replace(/_/g, ' ');
    // default project
    projectOption.dependentFieldIds =
      projectOption.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ')
        ? ['defaultProject']
        : [];
    return projectOption;
  });

  projectProperties.IsRequired = true;

  // Default project field
  let defaultProjectNameProperties = { ...properties };
  defaultProjectNameProperties.title = process.env.DEFAULT_PROJECT.replace(/_/g, ' ');
  defaultProjectNameProperties.type = 'options';
  defaultProjectNameProperties.isDefaultVisible = false;
  defaultProjectNameProperties.IsRequired = false;
  defaultProjectNameProperties.id = 'defaultProject';
  defaultProjectNameProperties.fieldSortOrder = 0;
  defaultProjectNameProperties.iconUrl = `${imageUrl}/ic_form_project.png`;
  defaultProjectNameProperties.hint = process.env.DEFAULT_PROJECT.replace(/_/g, ' ');
  defaultProjectNameProperties.parentFieldId = 'project';
  defaultProjectNameProperties.options = defaultProjectList.map(item => {
    let tempOption = { ...option };
    tempOption.title = item?.title;
    tempOption.isVisibleForOptions = [];
    tempOption.id = item?._id;
    tempOption.isDefault = true;
    // default project
    tempOption.dependentFieldIds =
      tempOption.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ') ? ['defaultProject'] : [];
    return tempOption;
  });

  // Location properties
  let locationProperties = { ...properties };
  locationProperties.title = 'Location';
  if (locationsList.length > 0) {
    locationProperties.type = 'options';
    locationProperties.hint = 'Select';
  } else {
    locationProperties.type = 'text';
    locationProperties.hint = 'Please Enter Location';
  }
  locationProperties.id = 'location';
  locationProperties.fieldSortOrder = 0;
  locationProperties.iconUrl = `${imageUrl}/ic_form_location.png`;
  locationProperties.options = locationsList.map(item => {
    let tempOption = { ...option };
    tempOption.title = item?.title;
    tempOption.isVisibleForOptions = [item?.project?._id];
    tempOption.id = item?._id;
    tempOption.isDefault = !!item?.isDefault;
    tempOption.dependentFieldIds =
      tempOption.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ') ? ['defaultProject'] : [];
    return tempOption;
  });
  locationProperties.IsRequired = true;
  locationProperties.parentFieldId = 'project';

  // Category properties
  let categoryProperties = { ...properties };
  categoryProperties.title = 'Category';
  categoryProperties.type = 'options';
  categoryProperties.hint = 'Select';
  categoryProperties.id = 'category';
  categoryProperties.fieldSortOrder = 0;
  categoryProperties.iconUrl = `${imageUrl}/ic_categories.png`;
  categoryProperties.options = categoryList.map(item => {
    let tempOption = { ...option };
    tempOption.title = item?.categoryName;
    tempOption.iconUrl = item?.iconUrl;
    tempOption.id = item?._id;
    tempOption.dependentFieldIds =
      tempOption.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ') ? ['defaultProject'] : [];
    return tempOption;
  });
  categoryProperties.IsRequired = true;

  // Severity properties
  let severityProperties = { ...properties };
  severityProperties.title = 'Severity';
  severityProperties.type = 'options';
  severityProperties.hint = 'Select';
  severityProperties.id = 'severity';
  severityProperties.fieldSortOrder = 0;
  severityProperties.iconUrl = `${imageUrl}/ic_form_saverity.png`;
  severityProperties.options = severityList.map(item => {
    let tempOption = { ...option };
    tempOption.title = item?.title;
    tempOption.color = item?.color;
    tempOption.id = item?._id;
    tempOption.dependentFieldIds =
      tempOption.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ') ? ['defaultProject'] : [];
    return tempOption;
  });
  severityProperties.IsRequired = true;

  // Likelihood properties
  let likelihoodProperties = { ...properties };
  likelihoodProperties.title = 'Likelihood';
  likelihoodProperties.type = 'options';
  likelihoodProperties.hint = 'Select';
  likelihoodProperties.id = 'likelihood';
  likelihoodProperties.fieldSortOrder = 0;
  likelihoodProperties.iconUrl = `${imageUrl}/ic_form_likely_hood.png`;
  likelihoodProperties.options = likelihoodList.map(item => {
    let tempOption = { ...option };
    tempOption.title = item?.title;
    tempOption.color = item?.color;
    tempOption.id = item?._id;
    tempOption.dependentFieldIds =
      tempOption.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ') ? ['defaultProject'] : [];
    return tempOption;
  });
  likelihoodProperties.IsRequired = true;

  // Description field
  let descriptionProperties = { ...properties };
  descriptionProperties.title = 'Description';
  descriptionProperties.type = 'textarea';
  descriptionProperties.fieldSortOrder = 0;
  descriptionProperties.id = 'description';
  descriptionProperties.iconUrl = `${imageUrl}/pencil-sharp.png`;
  descriptionProperties.hint = 'Please Enter Description';
  descriptionProperties.dependentFieldIds =
    descriptionProperties.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ')
      ? ['defaultProject']
      : [];

  // Action / Recommendation field
  let ActionRecomendationProperties = { ...properties };
  ActionRecomendationProperties.title = 'Actions / Recommendations';
  ActionRecomendationProperties.type = 'textarea';
  ActionRecomendationProperties.fieldSortOrder = 0;
  ActionRecomendationProperties.id = 'actionsTaken';
  ActionRecomendationProperties.iconUrl = `${imageUrl}/pencil-sharp.png`;
  ActionRecomendationProperties.hint = 'Please Enter Actions / Recommendations';
  ActionRecomendationProperties.dependentFieldIds =
    ActionRecomendationProperties.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ')
      ? ['defaultProject']
      : [];
  // Photo field
  let photoProperties = { ...properties };
  photoProperties.title = 'Images';
  photoProperties.type = 'images';
  photoProperties.id = 'images';
  photoProperties.fieldSortOrder = 0;
  photoProperties.iconUrl = `${imageUrl}/pencil-sharp.png`;
  photoProperties.selectedOption = [];
  photoProperties.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ') ? ['defaultProject'] : [];

  let unsafeFields = [
    titleProperties,
    projectProperties,
    defaultProjectNameProperties, // default project field
    locationProperties,
    categoryProperties,
    severityProperties,
    likelihoodProperties,
    descriptionProperties,
    ActionRecomendationProperties,
    photoProperties,
  ];

  if (!user.isMobile) {
    // Status field
    let StatusProperties = { ...properties };
    StatusProperties.title = 'Status';
    StatusProperties.type = 'options';
    StatusProperties.id = 'status';
    StatusProperties.fieldSortOrder = 0;
    StatusProperties.iconUrl = `${imageUrl}/pencil-sharp.png`;
    let opt = ['Open', 'Submitted', 'In Discussion', 'Closed', 'Archived'];
    StatusProperties.options = opt.map(item => {
      let tempOption = { ...option };
      tempOption.title = item;
      tempOption.iconUrl = item;
      tempOption.id = item.replace(/\s+/g, '_').toLowerCase();
      tempOption.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ') ? ['defaultProject'] : [];

      return tempOption;
    });
    unsafeFields.push(StatusProperties);
  }

  return unsafeFields;
};

const GetNCRCardStaticFile = async (user, status = true) => {
  let filterData = {
    account: user.account,
    ...(status && { isActive: true }),
    deletedAt: null,
    isDefault: false,
  };

  filterData = await this.getAndAddAssignProjectList(filterData, user);

  const projectList = await projectServices.getAllProjects({
    ...filterData,
    ...(this.projectStatus && { status: this.projectStatus }),
  });

  let filterLocation = {
    account: user.account,
    deletedAt: null,
  };
  filterLocation = await this.getAndAddAssignProjectList(filterLocation, user, 'location');
  const locationsList = await locationServices.getAllLocationByAccountId(filterLocation);
  // default project field
  const defaultProjectList = await projectServices.getDefaultProjects(user.account);

  // Title field
  let titleProperties = { ...properties };
  titleProperties.title = 'Title';
  titleProperties.type = 'text';
  titleProperties.id = 'title';
  titleProperties.fieldSortOrder = 0;
  titleProperties.iconUrl = `${imageUrl}/pencil-sharp.png`;
  titleProperties.hint = 'Please Enter Title';
  titleProperties.IsRequired = true;
  titleProperties.dependentFieldIds =
    titleProperties.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ')
      ? ['defaultProject']
      : [];
  // Project properties
  let projectProperties = { ...properties };
  projectProperties.title = 'Project';
  projectProperties.type = 'options';
  projectProperties.hint = 'Select';
  projectProperties.id = 'project';
  projectProperties.fieldSortOrder = 0;
  // default project
  projectProperties.hasChildField = true;
  projectProperties.iconUrl = `${imageUrl}/ic_form_project.png`;
  projectProperties.options = projectList.map(item => {
    let projectOption = { ...option };
    projectOption.title =
      item?.projectNumber !== '' ? `${item.projectNumber} - ${item.title}` : item?.title;
    projectOption.id = item?._id;
    projectOption.isDefault =
      projectOption.title === process.env.DEFAULT_PROJECT.replace(/_/g, ' ');
    projectOption.dependentFieldIds =
      projectOption.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ')
        ? ['defaultProject']
        : [];
    return projectOption;
  });
  projectProperties.IsRequired = true;

  // Default project field
  let defaultProjectNameProperties = { ...properties };
  defaultProjectNameProperties.title = process.env.DEFAULT_PROJECT.replace(/_/g, ' ');
  defaultProjectNameProperties.type = 'options';
  defaultProjectNameProperties.isDefaultVisible = false;
  defaultProjectNameProperties.IsRequired = false;
  defaultProjectNameProperties.id = 'defaultProject';
  defaultProjectNameProperties.fieldSortOrder = 0;
  defaultProjectNameProperties.iconUrl = `${imageUrl}/ic_form_project.png`;
  defaultProjectNameProperties.hint = process.env.DEFAULT_PROJECT.replace(/_/g, ' ');
  defaultProjectNameProperties.parentFieldId = 'project';
  defaultProjectNameProperties.options = defaultProjectList.map(item => {
    let tempOption = { ...option };
    tempOption.title = item?.title;
    tempOption.isVisibleForOptions = [];
    tempOption.id = item?._id;
    tempOption.isDefault = true;
    tempOption.dependentFieldIds =
      tempOption.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ') ? ['defaultProject'] : [];
    return tempOption;
  });

  // Location properties
  let locationProperties = { ...properties };
  locationProperties.title = 'Location';
  locationProperties.fieldSortOrder = 0;
  if (locationsList.length > 0) {
    locationProperties.type = 'options';
    locationProperties.hint = 'Select';
  } else {
    locationProperties.type = 'text';
    locationProperties.hint = 'Please Enter Location';
  }
  locationProperties.id = 'location';
  locationProperties.iconUrl = `${imageUrl}/ic_form_location.png`;
  locationProperties.options = locationsList.map(item => {
    let tempOption = { ...option };
    tempOption.title = item?.title;
    tempOption.id = item?._id;
    tempOption.isVisibleForOptions = [item?.project?._id];
    tempOption.isDefault = !!item?.isDefault;
    tempOption.dependentFieldIds =
      tempOption.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ') ? ['defaultProject'] : [];
    return tempOption;
  });
  locationProperties.IsRequired = true;
  locationProperties.parentFieldId = 'project';

  // Product Quality
  let toggleBoxProperties = { ...properties };
  toggleBoxProperties.title = 'Product Quality';
  toggleBoxProperties.type = 'boolean';
  toggleBoxProperties.id = 'productQuality';
  toggleBoxProperties.iconUrl = `${imageUrl}/ic_form_start.png`;
  toggleBoxProperties.IsRequired = false;
  toggleBoxProperties.hint = 'Product Quality';

  // Item/Component/Cable
  let itemProperties = { ...properties };
  itemProperties.title = 'Item / Component / Cable';
  itemProperties.type = 'text';
  itemProperties.fieldSortOrder = 0;
  itemProperties.id = 'item';
  itemProperties.iconUrl = `${imageUrl}/ic_form_component.png`;
  itemProperties.hint = 'Please Enter Item / Component / Cable';
  itemProperties.dependentFieldIds =
    itemProperties.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ')
      ? ['defaultProject']
      : [];

  // Description field
  let descriptionProperties = { ...properties };
  descriptionProperties.title = 'Description';
  descriptionProperties.type = 'textarea';
  descriptionProperties.fieldSortOrder = 0;
  descriptionProperties.id = 'description';
  descriptionProperties.iconUrl = `${imageUrl}/pencil-sharp.png`;
  descriptionProperties.hint = 'Please Enter Description';
  descriptionProperties.dependentFieldIds =
    descriptionProperties.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ')
      ? ['defaultProject']
      : [];

  // Corrective Actions / Recommendations field
  let correctiveActionRecomendationProperties = { ...properties };
  correctiveActionRecomendationProperties.title = 'Corrective actions / recommendations';
  correctiveActionRecomendationProperties.type = 'textarea';
  correctiveActionRecomendationProperties.fieldSortOrder = 0;
  correctiveActionRecomendationProperties.id = 'correctiveAction';
  correctiveActionRecomendationProperties.iconUrl = `${imageUrl}/pencil-sharp.png`;
  correctiveActionRecomendationProperties.hint =
    'Please Enter Corrective Actions / Recommendations';
  correctiveActionRecomendationProperties.dependentFieldIds =
    correctiveActionRecomendationProperties.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ')
      ? ['defaultProject']
      : [];

  // Actions / Recommendations field
  let preventiveActionRecomendationProperties = { ...properties };
  preventiveActionRecomendationProperties.title = 'Preventive actions / recommendations';
  preventiveActionRecomendationProperties.type = 'textarea';
  preventiveActionRecomendationProperties.fieldSortOrder = 0;
  preventiveActionRecomendationProperties.id = 'preventiveAction';
  preventiveActionRecomendationProperties.iconUrl = `${imageUrl}/pencil-sharp.png`;
  preventiveActionRecomendationProperties.hint =
    'Please Enter Preventive Actions / Recommendations';
  preventiveActionRecomendationProperties.dependentFieldIds =
    preventiveActionRecomendationProperties.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ')
      ? ['defaultProject']
      : [];
  // Photo field
  let photoProperties = { ...properties };
  photoProperties.title = 'Images';
  photoProperties.type = 'images';
  photoProperties.fieldSortOrder = 0;
  photoProperties.id = 'images';
  photoProperties.iconUrl = `${imageUrl}/pencil-sharp.png`;
  photoProperties.selectedOption = [];
  photoProperties.dependentFieldIds =
    photoProperties.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ')
      ? ['defaultProject']
      : [];
  let estimatedDelayCostProperties;
  if (!user.isMobile) {
    estimatedDelayCostProperties = { ...properties };
    estimatedDelayCostProperties.title = 'Estimated delay / cost';
    estimatedDelayCostProperties.type = 'text';
    estimatedDelayCostProperties.fieldSortOrder = 0;
    estimatedDelayCostProperties.id = 'estimatedDelayCost';
    estimatedDelayCostProperties.iconUrl = `${imageUrl}/pencil-sharp.png`;
    estimatedDelayCostProperties.selectedOption = [];
    estimatedDelayCostProperties.dependentFieldIds =
      estimatedDelayCostProperties.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ')
        ? ['defaultProject']
        : [];
  }
  let NCRFields = [
    titleProperties,
    projectProperties,
    defaultProjectNameProperties, // default project field
    locationProperties,
    toggleBoxProperties,
    itemProperties,
    descriptionProperties,
    correctiveActionRecomendationProperties,
    preventiveActionRecomendationProperties,
    photoProperties,
    estimatedDelayCostProperties,
  ].filter(field => field !== undefined);

  if (!user.isMobile) {
    // Status field
    let StatusProperties = { ...properties };
    StatusProperties.title = 'Status';
    StatusProperties.type = 'options';
    StatusProperties.fieldSortOrder = 0;
    StatusProperties.id = 'status';
    StatusProperties.iconUrl = `${imageUrl}/pencil-sharp.png`;
    let opt = ['Open', 'Submitted', 'In Discussion', 'Closed', 'Archived'];
    StatusProperties.options = opt.map(item => {
      let tempOption = { ...option };
      tempOption.title = item;
      tempOption.iconUrl = item;
      tempOption.id = item.replace(/\s+/g, '_').toLowerCase();
      tempOption.dependentFieldIds =
        tempOption.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ')
          ? ['defaultProject']
          : [];
      return tempOption;
    });
    NCRFields.push(StatusProperties);
  }

  return NCRFields;
};

const GetIncidentCardStaticFile = async (user, status = true) => {
  let filterData = {
    account: user.account,
    ...(status && { isActive: true }),
    deletedAt: null,
    isDefault: false,
  };

  filterData = await this.getAndAddAssignProjectList(filterData, user);

  const projectList = await projectServices.getAllProjects({
    ...filterData,
    ...(this.projectStatus && { status: this.projectStatus }),
  });

  let filterLocation = {
    account: user.account,
    deletedAt: null,
  };
  filterLocation = await this.getAndAddAssignProjectList(filterLocation, user, 'location');
  const locationsList = await locationServices.getAllLocationByAccountId(filterLocation);
  const severityList = await severityServices.getAllSeverity();
  const likelihoodList = await likelihoodServices.getAllLikelihood();
  let filterCategory = {
    account: toObjectId(user.account),
    isVisibleForIncidentCard: true,
    deletedAt: null,
  };
  const categoryList = await categoryServices.getAllCategory(filterCategory);
  const typeList = await typeServices.getAllType({ account: user.account });
  // default project field
  const defaultProjectList = await projectServices.getDefaultProjects(user.account);

  // Title field
  let titleProperties = { ...properties };
  titleProperties.title = 'Title';
  titleProperties.type = 'text';
  titleProperties.fieldSortOrder = 0;
  titleProperties.id = 'title';
  titleProperties.iconUrl = `${imageUrl}/pencil-sharp.png`;
  titleProperties.hint = 'Please Enter Title';
  titleProperties.IsRequired = true;
  titleProperties.IsRequired = true;
  titleProperties.dependentFieldIds =
    titleProperties.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ')
      ? ['defaultProject']
      : [];

  // Project properties
  let projectProperties = { ...properties };
  projectProperties.title = 'Project';
  projectProperties.type = 'options';
  projectProperties.hint = 'Select';
  projectProperties.id = 'project';
  projectProperties.fieldSortOrder = 0;
  // default project
  projectProperties.hasChildField = true;
  projectProperties.iconUrl = `${imageUrl}/ic_form_project.png`;
  projectProperties.options = projectList.map(item => {
    let projectOption = { ...option };
    projectOption.title =
      item?.projectNumber !== '' ? `${item.projectNumber} - ${item.title}` : item?.title;
    projectOption.id = item?._id;
    projectOption.isDefault =
      projectOption.title === process.env.DEFAULT_PROJECT.replace(/_/g, ' ');
    projectOption.dependentFieldIds =
      projectOption.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ')
        ? ['defaultProject']
        : [];
    return projectOption;
  });
  projectProperties.IsRequired = true;

  // Default project field
  let defaultProjectNameProperties = { ...properties };
  defaultProjectNameProperties.title = process.env.DEFAULT_PROJECT.replace(/_/g, ' ');
  defaultProjectNameProperties.type = 'options';
  defaultProjectNameProperties.isDefaultVisible = false;
  defaultProjectNameProperties.IsRequired = false;
  defaultProjectNameProperties.id = 'defaultProject';
  defaultProjectNameProperties.fieldSortOrder = 0;
  defaultProjectNameProperties.iconUrl = `${imageUrl}/ic_form_project.png`;
  defaultProjectNameProperties.hint = process.env.DEFAULT_PROJECT.replace(/_/g, ' ');
  defaultProjectNameProperties.parentFieldId = 'project';
  defaultProjectNameProperties.options = defaultProjectList.map(item => {
    let tempOption = { ...option };
    tempOption.title = item?.title;
    tempOption.isVisibleForOptions = [];
    tempOption.id = item?._id;
    tempOption.isDefault = true;
    tempOption.dependentFieldIds =
      tempOption.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ') ? ['defaultProject'] : [];
    return tempOption;
  });

  // Location properties
  let locationProperties = { ...properties };
  locationProperties.title = 'Location';
  locationProperties.fieldSortOrder = 0;
  if (locationsList.length > 0) {
    locationProperties.type = 'options';
    locationProperties.hint = 'Select';
  } else {
    locationProperties.type = 'text';
    locationProperties.hint = 'Please Enter Location';
  }
  locationProperties.id = 'location';
  locationProperties.iconUrl = `${imageUrl}/ic_form_location.png`;
  locationProperties.options = locationsList.map(item => {
    let tempOption = { ...option };
    tempOption.title = item?.title;
    tempOption.isVisibleForOptions = [item?.project?._id];
    tempOption.id = item?._id;
    tempOption.isDefault = !!item?.isDefault;
    tempOption.dependentFieldIds =
      tempOption.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ') ? ['defaultProject'] : [];
    return tempOption;
  });
  locationProperties.parentFieldId = 'project';
  locationProperties.IsRequired = true;

  // Category properties
  let categoryProperties = { ...properties };
  categoryProperties.title = 'Category';
  categoryProperties.fieldSortOrder = 0;
  categoryProperties.type = 'options';
  categoryProperties.hint = 'Select';
  categoryProperties.id = 'category';
  categoryProperties.iconUrl = `${imageUrl}/ic_categories.png`;
  categoryProperties.options = categoryList.map(item => {
    let tempOption = { ...option };
    tempOption.title = item?.categoryName;
    tempOption.iconUrl = item?.iconUrl;
    tempOption.id = item?._id;
    tempOption.dependentFieldIds =
      tempOption.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ') ? ['defaultProject'] : [];
    return tempOption;
  });
  categoryProperties.IsRequired = true;

  // Type properties
  let typeProperties = { ...properties };
  typeProperties.title = 'Type';
  typeProperties.fieldSortOrder = 0;
  typeProperties.type = 'options';
  typeProperties.hint = 'Select';
  typeProperties.id = 'type';
  typeProperties.iconUrl = `${imageUrl}/ic_form_type.png`;
  typeProperties.options = typeList.map(item => {
    let tempOption = { ...option };
    tempOption.title = item?.title;
    tempOption.color = item?.color;
    tempOption.id = item?._id;
    tempOption.dependentFieldIds =
      tempOption.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ') ? ['defaultProject'] : [];
    return tempOption;
  });
  typeProperties.IsRequired = true;

  // Time properties
  let timeProperties = { ...properties };
  timeProperties.title = 'Time on Incident';
  timeProperties.fieldSortOrder = 0;
  timeProperties.type = 'datetime';
  timeProperties.id = 'time';
  timeProperties.iconUrl = `${imageUrl}/ic_form_time.png`;
  timeProperties.IsRequired = true;
  timeProperties.hint = 'Please Enter Time On Incident';
  timeProperties.dependentFieldIds =
    timeProperties.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ')
      ? ['defaultProject']
      : [];
  // Severity properties
  let severityProperties = { ...properties };
  severityProperties.title = 'Severity';
  severityProperties.fieldSortOrder = 0;
  severityProperties.type = 'options';
  severityProperties.hint = 'Select';
  severityProperties.id = 'severity';
  severityProperties.iconUrl = `${imageUrl}/ic_form_saverity.png`;
  severityProperties.options = severityList.map(item => {
    let tempOption = { ...option };
    tempOption.title = item?.title;
    tempOption.color = item?.color;
    tempOption.id = item?._id;
    tempOption.dependentFieldIds =
      tempOption.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ') ? ['defaultProject'] : [];
    return tempOption;
  });
  severityProperties.IsRequired = true;

  // Likelihood properties
  let likelihoodProperties = { ...properties };
  likelihoodProperties.title = 'Likelihood';
  likelihoodProperties.fieldSortOrder = 0;
  likelihoodProperties.type = 'options';
  likelihoodProperties.hint = 'Select';
  likelihoodProperties.id = 'likelihood';
  likelihoodProperties.iconUrl = `${imageUrl}/ic_form_likely_hood.png`;
  likelihoodProperties.options = likelihoodList.map(item => {
    let tempOption = { ...option };
    tempOption.title = item?.title;
    tempOption.color = item?.color;
    tempOption.id = item?._id;
    tempOption.dependentFieldIds =
      tempOption.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ') ? ['defaultProject'] : [];
    return tempOption;
  });
  likelihoodProperties.IsRequired = true;

  // Description field
  let descriptionProperties = { ...properties };
  descriptionProperties.title = 'Description';
  descriptionProperties.fieldSortOrder = 0;
  descriptionProperties.type = 'textarea';
  descriptionProperties.id = 'description';
  descriptionProperties.iconUrl = `${imageUrl}/pencil-sharp.png`;
  descriptionProperties.hint = 'Please Enter Description';
  descriptionProperties.dependentFieldIds =
    descriptionProperties.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ')
      ? ['defaultProject']
      : [];

  // Actions / Recommendations field
  let ActionRecomendationProperties = { ...properties };
  ActionRecomendationProperties.title = 'Actions / Recommendations';
  ActionRecomendationProperties.fieldSortOrder = 0;
  ActionRecomendationProperties.type = 'textarea';
  ActionRecomendationProperties.id = 'actionsTaken';
  ActionRecomendationProperties.iconUrl = `${imageUrl}/pencil-sharp.png`;
  ActionRecomendationProperties.hint = 'Please Enter Actions / Recommendations';
  ActionRecomendationProperties.dependentFieldIds =
    ActionRecomendationProperties.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ')
      ? ['defaultProject']
      : [];
  // Photo field
  let photoProperties = { ...properties };
  photoProperties.title = 'Images';
  photoProperties.fieldSortOrder = 0;
  photoProperties.type = 'images';
  photoProperties.id = 'images';
  photoProperties.iconUrl = `${imageUrl}/pencil-sharp.png`;
  photoProperties.selectedOption = [];
  photoProperties.dependentFieldIds =
    photoProperties.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ')
      ? ['defaultProject']
      : [];
  let incidentFields = [
    titleProperties,
    projectProperties,
    defaultProjectNameProperties, // default project field
    locationProperties,
    categoryProperties,
    typeProperties,
    timeProperties,
    severityProperties,
    likelihoodProperties,
    descriptionProperties,
    ActionRecomendationProperties,
    photoProperties,
  ];

  if (!user.isMobile) {
    // Status field
    let StatusProperties = { ...properties };
    StatusProperties.title = 'Status';
    StatusProperties.fieldSortOrder = 0;
    StatusProperties.type = 'options';
    StatusProperties.id = 'status';
    StatusProperties.iconUrl = `${imageUrl}/pencil-sharp.png`;
    let opt = ['Open', 'Submitted', 'In Discussion', 'Closed', 'Archived'];
    StatusProperties.options = opt.map(item => {
      let tempOption = { ...option };
      tempOption.title = item;
      tempOption.iconUrl = item;
      tempOption.id = item.replace(/\s+/g, '_').toLowerCase();
      tempOption.dependentFieldIds =
        tempOption.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ')
          ? ['defaultProject']
          : [];
      return tempOption;
    });
    incidentFields.push(StatusProperties);
  }

  return incidentFields;
};

const getShiftStaticFile = async (user, status = true) => {
  let filterData = {
    account: user.account,
    ...(status && { isActive: true }),
    deletedAt: null,
    isDefault: false,
  };

  const filter = {
    account: user.account,
    ...(status && { isActive: true }),
    deletedAt: null,
  };

  filterData = await this.getAndAddAssignProjectList(filterData, user);

  const projectList = await projectServices.getAllProjects({
    ...filterData,
    ...(this.projectStatus && { status: this.projectStatus }),
  });
  const teamList = await teamServices.getAllTeam(filter);
  const memberList = await memberServices.getAllAccountMember(user.account);
  const defaultProjectList = await projectServices.getDefaultProjects(user.account);

  // Shift properties

  // Project properties
  let projectProperties = { ...properties };
  projectProperties.title = 'Project';
  projectProperties.type = 'options';
  projectProperties.hint = 'Select';
  projectProperties.id = 'project';
  // default project
  projectProperties.hasChildField = true;
  projectProperties.fieldSortOrder = 0;
  projectProperties.iconUrl = `${imageUrl}/ic_form_project.png`;
  projectProperties.options = projectList.map(item => {
    let projectOption = { ...option };
    projectOption.title =
      item?.projectNumber !== '' ? `${item.projectNumber} - ${item.title}` : item?.title;
    projectOption.id = item?._id;
    projectOption.isDefault =
      projectOption.title === process.env.DEFAULT_PROJECT.replace(/_/g, ' ');
    // default project
    projectOption.dependentFieldIds =
      projectOption.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ')
        ? ['defaultProject']
        : [];
    return projectOption;
  });
  projectProperties.IsRequired = true;

  // Teams
  let teamProperties = { ...properties };
  teamProperties.title = 'Team';
  teamProperties.type = 'options';
  teamProperties.hint = 'Select';
  teamProperties.id = 'team';
  teamProperties.type = 'options';
  teamProperties.iconUrl = `${imageUrl}/ic_form_team.png`;
  teamProperties.options = teamList.map(item => {
    let teamOption = { ...option };
    teamOption.title = item?.teamsWfmName;
    teamOption.isVisibleForOptions = [item?.project?._id];
    teamOption.id = item?._id;
    teamOption.isDefault = !!item?.isDefault;
    teamOption.dependentFieldIds =
      teamOption.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ') ? ['defaultProject'] : [];
    return teamOption;
  });
  teamProperties.IsRequired = true;
  teamProperties.parentFieldId = 'project';

  // Start Date and Time properties
  let timeProperties = { ...properties };
  timeProperties.title = 'Start Date';
  timeProperties.type = 'datetime';
  timeProperties.id = 'startDate';
  timeProperties.iconUrl = `${imageUrl}/ic_form_start.png`;
  timeProperties.IsRequired = true;
  timeProperties.hint = 'Please Enter Start Date and Time of Shift';
  timeProperties.dependentFieldIds =
    timeProperties.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ')
      ? ['defaultProject']
      : [];

  // Checkbox properties
  let checkboxProperties = { ...properties };
  checkboxProperties.title = 'Not In List';
  checkboxProperties.type = 'checkbox';
  checkboxProperties.id = 'notInList';
  checkboxProperties.iconUrl = `${imageUrl}/ic_form_start.png`;
  checkboxProperties.IsRequired = true;
  checkboxProperties.hint = '';
  checkboxProperties.options = [
    {
      ...option,
      title: 'Not In List',
      id: 'notInList',
    },
  ];
  checkboxProperties.parentFieldId = 'project';

  // Member Name properties
  let memberNameProperties = { ...properties };
  memberNameProperties.title = 'Member Name';
  memberNameProperties.type = 'text';
  memberNameProperties.id = 'memberName';
  memberNameProperties.iconUrl = `${imageUrl}/ic_form_start.png`;
  memberNameProperties.IsRequired = true;
  memberNameProperties.isDefaultVisible = false;
  memberNameProperties.hint = 'Please enter Member Name';
  memberNameProperties.dependentFieldIds =
    memberNameProperties.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ')
      ? ['defaultProject']
      : [];

  // Function Name properties
  let functionNameProperties = { ...properties };
  functionNameProperties.title = 'Function Name';
  functionNameProperties.type = 'text';
  functionNameProperties.id = 'functionName';
  functionNameProperties.iconUrl = `${imageUrl}/ic_form_start.png`;
  functionNameProperties.IsRequired = true;
  functionNameProperties.hint = 'Please enter Function Name';
  functionNameProperties.isDefaultVisible = false;
  functionNameProperties.dependentFieldIds =
    functionNameProperties.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ')
      ? ['defaultProject']
      : [];

  // Button field
  let buttonProperties = { ...properties };
  buttonProperties.title = 'Add In Member';
  buttonProperties.type = 'button';
  buttonProperties.fieldSortOrder = 0;
  buttonProperties.id = 'button';
  buttonProperties.iconUrl = `${imageUrl}/pencil-sharp.png`;
  buttonProperties.selectedOption = [];
  buttonProperties.parentFieldId = '';

  // Default project field
  let defaultProjectNameProperties = { ...properties };
  defaultProjectNameProperties.title = process.env.DEFAULT_PROJECT.replace(/_/g, ' ');
  defaultProjectNameProperties.type = 'options';
  defaultProjectNameProperties.isDefaultVisible = false;
  defaultProjectNameProperties.IsRequired = false;
  defaultProjectNameProperties.id = 'defaultProject';
  defaultProjectNameProperties.fieldSortOrder = 0;
  defaultProjectNameProperties.iconUrl = `${imageUrl}/pencil-sharp.png`;
  defaultProjectNameProperties.hint = process.env.DEFAULT_PROJECT.replace(/_/g, ' ');
  defaultProjectNameProperties.parentFieldId = 'project';
  defaultProjectNameProperties.options = defaultProjectList.map(item => {
    let tempOption = { ...option };
    tempOption.title = item?.title;
    tempOption.isVisibleForOptions = [];
    tempOption.id = item?._id;
    tempOption.isDefault = true;
    tempOption.dependentFieldIds =
      tempOption.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ') ? ['defaultProject'] : [];
    return tempOption;
  });

  // Member Properties
  let memberProperties = { ...properties };
  memberProperties.title = 'Member';
  memberProperties.type = 'options';
  memberProperties.hint = 'Select';
  memberProperties.id = 'member';
  memberProperties.type = 'multi-options';
  memberProperties.iconUrl = `${imageUrl}/ic_form_member.png`;

  memberProperties.options = memberList.map(item => {
    let teamOption = { ...option };

    let newName;
    if (
      item?.user?.callingName !== undefined &&
      item?.user?.callingName !== null &&
      item?.user?.callingName !== ''
    ) {
      newName = item?.user?.callingName.trim();
    } else if (
      item?.user?.firstName !== undefined &&
      item?.user?.firstName !== null &&
      item?.user?.firstName !== ''
    ) {
      newName = item?.user?.firstName; // default value
    } else {
      newName = 'N/A'; // default value
    }
    teamOption.title = newName + ' ' + item?.user?.lastName;
    teamOption.isVisibleForOptions = [item?.project?._id];
    teamOption.id = item?._id;
    teamOption.dependentFieldIds =
      teamOption.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ') ? ['defaultProject'] : [];
    return teamOption;
  });
  memberProperties.IsRequired = true;
  memberProperties.parentFieldId = 'project';

  return [
    projectProperties,
    defaultProjectNameProperties,
    teamProperties,
    timeProperties,
    memberProperties,
    checkboxProperties,
    memberNameProperties,
    functionNameProperties,
    buttonProperties,
  ];
};

const getReportStaticFile = async (user, status = true) => {
  let filterData = {
    account: user.account,
    ...(status && { isActive: true }),
    deletedAt: null,
  };
  filterData = await this.getAndAddAssignProjectList(filterData, user);
  const projectList = await projectServices.getAllProjects({
    ...filterData,
    ...(this.projectStatus && { status: this.projectStatus }),
  });

  let filterCommon = {
    account: user.account,
    deletedAt: null,
  };
  filterCommon = await this.getAndAddAssignProjectList(filterCommon, user, 'location');
  const locationsList = await locationServices.getAllLocationByAccountId(filterCommon);
  const assetList = await assetServices.getAllAssetByAccountId(filterCommon);

  let filterReport = {
    account: user.account,
    deletedAt: null,
  };
  filterReport = await this.getAndAddAssignProjectList(filterReport, user, 'report');
  const reportTypeList = await reportTypeServices.getReportTypeByAccountId(filterReport);

  // Report properties

  // Project
  let projectProperties = { ...properties };
  projectProperties.title = 'Project';
  projectProperties.type = 'options';
  projectProperties.hint = 'Select';
  projectProperties.id = 'project';
  projectProperties.iconUrl = `${imageUrl}/project.png`;
  projectProperties.options = projectList.map(item => {
    let projectOption = { ...option };
    projectOption.title =
      item?.projectNumber !== '' ? `${item.projectNumber} - ${item.title}` : item?.title;
    projectOption.id = item?._id;
    projectOption.isDefault =
      projectOption.title === process.env.DEFAULT_PROJECT.replace(/_/g, ' ');
    return projectOption;
  });
  projectProperties.IsRequired = true;

  // Location properties
  let locationProperties = { ...properties };
  locationProperties.title = 'Location';
  if (locationsList.length > 0) {
    locationProperties.type = 'options';
    locationProperties.hint = 'Select';
  } else {
    locationProperties.type = 'text';
    locationProperties.hint = 'Please Enter Location';
  }
  locationProperties.id = 'location';
  locationProperties.iconUrl = `${imageUrl}/caret-forward-circle-sharp.png`;
  locationProperties.options = locationsList.map(item => {
    let tempOption = { ...option };
    tempOption.title = item?.title;
    tempOption.isVisibleForOptions = [item?.project?._id];
    tempOption.id = item?._id;
    return tempOption;
  });
  locationProperties.parentFieldId = 'project';
  locationProperties.IsRequired = true;

  // Asset Properties
  let assetProperties = { ...properties };
  assetProperties.title = 'Asset';
  assetProperties.type = 'options';
  assetProperties.hint = 'Select';
  assetProperties.id = 'cable';
  assetProperties.type = 'options';
  assetProperties.iconUrl = `${imageUrl}/asset.png`;
  assetProperties.options = assetList.map(item => {
    let tempOption = { ...option };
    tempOption.title = item?.cableName;
    tempOption.isVisibleForOptions = [item?.fromLocation?._id, item?.toLocation?._id];
    tempOption.id = item?._id;
    return tempOption;
  });
  assetProperties.IsRequired = true;
  assetProperties.parentFieldId = 'location';

  // Report Type Properties
  let reportTypeProperties = { ...properties };
  reportTypeProperties.title = 'Report Type';
  reportTypeProperties.type = 'options';
  reportTypeProperties.hint = 'Select';
  reportTypeProperties.id = 'report-type';
  reportTypeProperties.type = 'options';
  reportTypeProperties.iconUrl = `${imageUrl}/report-type.png`;
  reportTypeProperties.options = reportTypeList.map(item => {
    let tempOption = { ...option };
    tempOption.title = item?.terminationTypeName;
    tempOption.isVisibleForOptions = [item?.project?._id];
    tempOption.id = item?._id;
    return tempOption;
  });

  reportTypeProperties.IsRequired = true;
  reportTypeProperties.parentFieldId = 'project';

  return [projectProperties, locationProperties, assetProperties, reportTypeProperties];
};

const getShiftActivityStaticFile = async user => {
  let filterData = {
    account: user.account,
    deletedAt: null,
  };
  filterData = await this.getAndAddAssignProjectList(filterData, user, 'activity');
  const activityList = await activityServices.getAllActivity(filterData);
  let filterCommon = {
    account: user.account,
    deletedAt: null,
  };
  filterCommon = await this.getAndAddAssignProjectList(filterCommon, user, 'location');
  const locationsList = await locationServices.getAllLocationByAccountId(filterCommon);

  // Shift activity properties

  // Activity
  let activityProperties = { ...properties };
  activityProperties.title = 'Activity';
  activityProperties.type = 'options';
  activityProperties.hint = 'Select';
  activityProperties.id = 'activity';
  activityProperties.iconUrl = `${imageUrl}/ic_form_activity.png`;
  activityProperties.options = activityList.map(item => {
    let activityOption = { ...option };
    activityOption.title = item?.name;
    activityOption.isVisibleForOptions = [item?.project?._id];
    activityOption.isDefault = !!item?.isDefault;
    activityOption.dependentFieldIds =
      activityOption.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ')
        ? ['defaultProject']
        : [];
    activityOption.id = item?._id;
    return activityOption;
  });
  activityProperties.IsRequired = true;
  activityProperties.parentFieldId = 'project';

  // Location properties
  let locationProperties = { ...properties };
  locationProperties.title = 'Location';
  if (locationsList.length > 0) {
    locationProperties.type = 'options';
    locationProperties.hint = 'Select';
  } else {
    locationProperties.type = 'text';
    locationProperties.hint = 'Please Enter Location';
  }
  locationProperties.id = 'location';
  locationProperties.iconUrl = `${imageUrl}/ic_form_location.png`;
  locationProperties.options = locationsList.map(item => {
    let tempOption = { ...option };
    tempOption.title = item?.title;
    tempOption.isVisibleForOptions = [item?.project?._id];
    tempOption.isDefault = !!item?.isDefault;
    tempOption.dependentFieldIds =
      tempOption.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ') ? ['defaultProject'] : [];
    tempOption.id = item?._id;
    return tempOption;
  });
  locationProperties.IsRequired = true;
  locationProperties.parentFieldId = 'project';

  // Start Date and Time properties
  let timeProperties = { ...properties };
  timeProperties.title = 'End Date';
  timeProperties.type = 'datetime';
  timeProperties.id = 'endTime';
  timeProperties.iconUrl = `${imageUrl}/ic_form_end_time.png`;
  timeProperties.IsRequired = true;
  timeProperties.hint = 'Please Enter End Date and Time of Activity';

  // Description field
  let descriptionProperties = { ...properties };
  descriptionProperties.title = 'Description';
  descriptionProperties.type = 'textarea';
  descriptionProperties.id = 'comments';
  descriptionProperties.iconUrl = `${imageUrl}/pencil-sharp.png`;
  descriptionProperties.hint = 'Please Enter Description';

  return [activityProperties, locationProperties, timeProperties, descriptionProperties];
};

const getFeedbackStaticFile = async () => {
  // Type properties
  let typeProperties = { ...properties };
  typeProperties.title = 'Type';
  typeProperties.type = 'options';
  typeProperties.hint = 'Select';
  typeProperties.id = 'type';
  typeProperties.iconUrl = `${imageUrl}/ic_form_submit_feedback_type.png`;
  typeProperties.options = feedback.Types;
  typeProperties.IsRequired = true;

  // Subject properties
  let subjectProperties = { ...properties };
  subjectProperties.title = 'Subject';
  subjectProperties.type = 'options';
  subjectProperties.hint = 'Select';
  subjectProperties.id = 'subject';
  subjectProperties.iconUrl = `${imageUrl}/ic_form_subject.png`;
  subjectProperties.options = feedback.Subjects;
  subjectProperties.IsRequired = true;

  // Description field
  let descriptionProperties = { ...properties };
  descriptionProperties.title = 'Description';
  descriptionProperties.type = 'textarea';
  descriptionProperties.id = 'description';
  descriptionProperties.iconUrl = `${imageUrl}/pencil-sharp.png`;
  descriptionProperties.hint = 'Please Enter Description';
  descriptionProperties.IsRequired = true;

  // Photo field
  let photoProperties = { ...properties };
  photoProperties.title = 'Images';
  photoProperties.type = 'images';
  photoProperties.id = 'images';
  photoProperties.iconUrl = `${imageUrl}/pencil-sharp.png`;
  photoProperties.selectedOption = [];

  return [typeProperties, subjectProperties, descriptionProperties, photoProperties];
};

const GetEquipmentStaticFile = async (user, status = true) => {
  const filterData = {
    account: user.account,
    ...(status && { isActive: true }),
    deletedAt: null,
  };

  const equipmentCategoryList = await equipmentCategoryServices.getEquipmentCategory(filterData);
  const equipmentTypeList = await equipmentTypeServices.getEquipmentType(filterData);
  const equipmentUnitList = await equipmentUnitServices.getEquipmentUnit(filterData);
  const quantityTypeList = await quantityTypeServices.getEquipmentQuantityType(filterData);
  const hsCodeList = await hsCodeServices.getHSCode(filterData, '', '', -1);

  // equipment name field
  let nameProperties = { ...properties };
  nameProperties.title = 'Name';
  nameProperties.type = 'search';
  nameProperties.id = 'name';
  nameProperties.fieldSortOrder = 0;
  nameProperties.iconUrl = `${imageUrl}/pencil-sharp.png`;
  nameProperties.hint = 'Please Enter Equipment Name';
  nameProperties.IsRequired = true;

  // serial number field
  let serialNumberProperties = { ...properties };
  serialNumberProperties.title = 'Serial Number';
  serialNumberProperties.type = 'text';
  serialNumberProperties.id = 'serialNumber';
  serialNumberProperties.fieldSortOrder = 0;
  serialNumberProperties.iconUrl = `${imageUrl}/pencil-sharp.png`;
  serialNumberProperties.hint = 'Please Enter Serial Number';
  serialNumberProperties.IsRequired = false;
  serialNumberProperties.isDefaultVisible = false;
  serialNumberProperties.parentFieldId = 'equipmentType';
  serialNumberProperties.dependentIds = equipmentTypeList
    .filter(
      element => element.quantityType.quantityType == 'unique' && element.quantityType.isActive
    )
    .map(element => element._id);

  // Rental Price field
  let priceProperties = { ...properties };
  priceProperties.title = 'Rental price per day';
  priceProperties.type = 'options';
  priceProperties.hint = 'Select';
  priceProperties.id = 'price';
  priceProperties.fieldSortOrder = 0;
  priceProperties.iconUrl = `${imageUrl}/project.png`;
  priceProperties.options = equipmentTypeList.map(item => {
    let priceOption = { ...option };
    priceOption.title = item?.price?.toString();
    priceOption.id = item?._id;
    priceOption.isVisibleForOptions = [item?._id || ''];
    return priceOption;
  });
  priceProperties.IsRequired = false;
  priceProperties.parentFieldId = 'equipmentType';
  priceProperties.isDefaultVisible = false;
  priceProperties.dependentIds = equipmentTypeList
    .filter(element => element.quantityType.priceType == 'rental')
    .map(element => element._id);

  // Buy Price Field
  let buyPriceProperties = { ...properties };
  buyPriceProperties.title = 'Buy price';
  buyPriceProperties.type = 'options';
  buyPriceProperties.hint = 'Select';
  buyPriceProperties.id = 'price';
  buyPriceProperties.fieldSortOrder = 0;
  buyPriceProperties.iconUrl = `${imageUrl}/project.png`;
  buyPriceProperties.IsRequired = false;
  buyPriceProperties.isDefaultVisible = false;
  buyPriceProperties.parentFieldId = 'equipmentType';
  buyPriceProperties.options = equipmentTypeList.map(item => {
    let priceOption = { ...option };
    priceOption.title = item?.price?.toString();
    priceOption.id = item?._id;
    priceOption.isVisibleForOptions = [item?._id || ''];
    return priceOption;
  });
  buyPriceProperties.parentFieldId = 'equipmentType';
  buyPriceProperties.dependentIds = equipmentTypeList
    .filter(element => element.quantityType.priceType == 'buy')
    .map(element => element._id);

  // weight field
  let weightProperties = { ...properties };
  weightProperties.title = 'Weight';
  weightProperties.type = 'decimal-number';
  weightProperties.id = 'weight';
  weightProperties.fieldSortOrder = 0;
  weightProperties.iconUrl = `${imageUrl}/pencil-sharp.png`;
  weightProperties.hint = 'Please Enter Weight Number';
  weightProperties.IsRequired = true;

  // Equipment Type properties
  let equipmentTypeProperties = { ...properties };
  equipmentTypeProperties.title = 'Equipment Type';
  equipmentTypeProperties.type = 'options';
  equipmentTypeProperties.hint = 'Select';
  equipmentTypeProperties.id = 'equipmentType';
  equipmentTypeProperties.fieldSortOrder = 0;
  equipmentTypeProperties.iconUrl = `${imageUrl}/project.png`;
  equipmentTypeProperties.options = equipmentTypeList.map(item => {
    let equipmentTypeOption = { ...option };
    equipmentTypeOption.title = item?.type;
    equipmentTypeOption.currency = item?.currencyUnit?.symbol;
    equipmentTypeOption.id = item?._id;
    return equipmentTypeOption;
  });
  equipmentTypeProperties.IsRequired = true;

  // Equipment Category properties
  let equipmentCategoryProperties = { ...properties };
  equipmentCategoryProperties.title = 'Equipment Category';
  equipmentCategoryProperties.type = 'options';
  equipmentCategoryProperties.hint = 'Select';
  equipmentCategoryProperties.id = 'equipmentCategory';
  equipmentCategoryProperties.fieldSortOrder = 0;
  equipmentCategoryProperties.iconUrl = `${imageUrl}/project.png`;
  equipmentCategoryProperties.options = equipmentCategoryList.map(item => {
    let equipmentCategoryOption = { ...option };
    equipmentCategoryOption.title = item?.name;
    equipmentCategoryOption.id = item?._id;
    equipmentCategoryOption.isVisibleForOptions = equipmentTypeList
      .filter(element => element?.equipmentCategory?._id?.toString() === item?._id?.toString())
      .map(element => element._id || '');

    return equipmentCategoryOption;
  });
  equipmentCategoryProperties.IsRequired = true;
  equipmentCategoryProperties.parentFieldId = 'equipmentType';

  // Weight Form properties
  let weightFormProperties = { ...properties };
  weightFormProperties.title = 'Equipment Unit';
  weightFormProperties.type = 'options';
  weightFormProperties.hint = 'Select';
  weightFormProperties.id = 'weightForm';
  weightFormProperties.fieldSortOrder = 0;
  weightFormProperties.iconUrl = `${imageUrl}/project.png`;
  weightFormProperties.options = equipmentUnitList.map(item => {
    let weightFormOption = { ...option };
    weightFormOption.title = item?.title;
    weightFormOption.abbrevation = item?.abbrevation;
    weightFormOption.id = item?._id;
    weightFormOption.isVisibleForOptions = equipmentTypeList
      .filter(element => element?.equipmentUnit?._id?.toString() === item?._id?.toString())
      .map(element => element._id || '');

    return weightFormOption;
  });
  weightFormProperties.IsRequired = true;
  weightFormProperties.parentFieldId = 'equipmentType';

  // Quantity Type properties
  let quantityTypeProperties = { ...properties };
  quantityTypeProperties.title = 'Quantity Type';
  quantityTypeProperties.type = 'options';
  quantityTypeProperties.hint = 'Select';
  quantityTypeProperties.id = 'quantityType';
  quantityTypeProperties.fieldSortOrder = 0;
  quantityTypeProperties.iconUrl = `${imageUrl}/project.png`;
  quantityTypeProperties.options = quantityTypeList.map(item => {
    let quantityTypeOption = { ...option };
    let inputString = item?.priceType;
    let quantityType = item?.quantityType;
    quantityTypeOption.title =
      item?.name +
      ' (' +
      inputString.charAt(0).toUpperCase() +
      inputString.slice(1) +
      ',' +
      quantityType.charAt(0).toUpperCase() +
      quantityType.slice(1) +
      ')';
    quantityTypeOption.id = item?._id;
    quantityTypeOption.isVisibleForOptions = equipmentTypeList
      .filter(element => element?.quantityType?._id?.toString() === item?._id?.toString())
      .map(element => element._id || '');

    return quantityTypeOption;
  });
  quantityTypeProperties.IsRequired = true;
  quantityTypeProperties.parentFieldId = 'equipmentType';

  // Total Value properties
  let totalValueProperties = { ...properties };
  totalValueProperties.title = 'Total purchase value';
  totalValueProperties.type = 'options-values';
  totalValueProperties.hint = 'Please Enter Total Value';
  totalValueProperties.id = 'value';
  totalValueProperties.fieldSortOrder = 0;
  totalValueProperties.iconUrl = `${imageUrl}/project.png`;
  totalValueProperties.IsRequired = false;
  totalValueProperties.isDefaultVisible = false;
  totalValueProperties.parentFieldId = 'equipmentType';
  totalValueProperties.dependentIds = equipmentTypeList
    .filter(element => element.quantityType.priceType == 'rental')
    .map(element => element._id);

  // HS Code properties
  let hsCodeProperties = { ...properties };
  hsCodeProperties.title = 'HS Code';
  hsCodeProperties.type = 'options';
  hsCodeProperties.hint = 'Select';
  hsCodeProperties.id = 'hsCode';
  hsCodeProperties.fieldSortOrder = 0;
  hsCodeProperties.iconUrl = `${imageUrl}/project.png`;
  hsCodeProperties.options = hsCodeList.map(item => {
    let hsCodeOption = { ...option };
    hsCodeOption.title = item?.name + '(' + item?.code + ')';
    hsCodeOption.code = item?.code.toString();
    hsCodeOption.id = item?._id;
    hsCodeOption.isVisibleForOptions = equipmentTypeList
      .filter(element => element?.hsCode?._id?.toString() === item?._id?.toString())
      .map(element => element._id || '');

    return hsCodeOption;
  });
  hsCodeProperties.IsRequired = true;
  hsCodeProperties.parentFieldId = 'equipmentType';

  return [
    nameProperties,
    equipmentTypeProperties,
    equipmentCategoryProperties,
    weightFormProperties,
    weightProperties,
    quantityTypeProperties,
    hsCodeProperties,
    priceProperties,
    buyPriceProperties,
    totalValueProperties,
    serialNumberProperties,
  ];
};

const GetImageSpecificationAndPriceStaticFile = async user => {
  const filterData = {
    account: user.account,
    deletedAt: null,
  };

  const equipmentCertificateType = await certificateTypeServices.getEquipmentCertificateType(
    filterData
  );
  const equipmentTypeList = await equipmentTypeServices.getEquipmentType(filterData);

  // Photo field
  let photoProperties = { ...properties };
  photoProperties.title = 'Upload photo';
  photoProperties.type = 'images';
  photoProperties.fieldSortOrder = 0;
  photoProperties.id = 'equipmentImage';
  photoProperties.iconUrl = `${imageUrl}/pencil-sharp.png`;
  photoProperties.selectedOption = [];
  photoProperties.IsRequired = false;

  // Upload certificate field
  let certificateProperties = { ...properties };
  certificateProperties.title = 'Certificate (PDF, image)';
  certificateProperties.type = 'certificate-images';
  certificateProperties.fieldSortOrder = 0;
  certificateProperties.id = 'certificate';
  certificateProperties.iconUrl = `${imageUrl}/pencil-sharp.png`;
  certificateProperties.selectedOption = [];
  certificateProperties.IsRequired = false;
  certificateProperties.isDefaultVisible = false;

  // Start Date and Time properties
  let startDateProperties = { ...properties };
  startDateProperties.title = 'Certificate start date';
  startDateProperties.type = 'validity-date';
  startDateProperties.id = 'startDate';
  startDateProperties.iconUrl = `${imageUrl}/caret-forward-circle-sharp.png`;
  startDateProperties.IsRequired = false;
  startDateProperties.isDefaultVisible = false;
  startDateProperties.hint = 'Please Enter Certificate Start Date';
  startDateProperties.parentFieldId = 'certificateType';
  startDateProperties.dependentIds = equipmentCertificateType
    .filter(element => element?.isValidityDate === true)
    .map(element => element._id || '');

  // End Date and Time properties
  let endDateProperties = { ...properties };
  endDateProperties.title = 'Certificate end date';
  endDateProperties.type = 'validity-date';
  endDateProperties.id = 'endDate';
  endDateProperties.iconUrl = `${imageUrl}/caret-forward-circle-sharp.png`;
  endDateProperties.IsRequired = false;
  endDateProperties.isDefaultVisible = false;
  endDateProperties.hint = 'Please Enter Certificate End Date';
  endDateProperties.parentFieldId = 'certificateType';
  endDateProperties.dependentIds = equipmentCertificateType
    .filter(element => element?.isValidityDate === true)
    .map(element => element._id || '');

  // Certificate type properties
  let certificateTypeProperties = { ...properties };
  certificateTypeProperties.title = 'Certificate Type';
  certificateTypeProperties.type = 'certificateType';
  certificateTypeProperties.hint = 'Select';
  certificateTypeProperties.id = 'certificateType';
  certificateTypeProperties.fieldSortOrder = 0;
  certificateTypeProperties.iconUrl = `${imageUrl}/project.png`;
  certificateTypeProperties.options = equipmentCertificateType.map(item => {
    let certificateTypeOption = { ...option };
    certificateTypeOption.title = item?.title;
    certificateTypeOption.id = item?._id;
    certificateTypeOption.hasEmptyValue = !item?.isValidityDate;
    certificateTypeOption.dependentFieldIds = equipmentTypeList
      .filter(element =>
        element?.certificateTypes.some(
          certificate => certificate._id.toString() === item._id.toString()
        )
      )
      .map(element => element._id || '');
    return certificateTypeOption;
  });
  certificateTypeProperties.IsRequired = false;
  certificateTypeProperties.isDefaultVisible = true;
  certificateTypeProperties.parentFieldId = 'equipmentType';

  return [
    photoProperties,
    certificateTypeProperties,
    certificateProperties,
    startDateProperties,
    endDateProperties,
  ];
};

const GetWarehouseInfoFields = async user => {
  const filterData = {
    account: toObjectId(user.account),
    deletedAt: { $eq: null },
    isActive: true,
  };

  const equipmentTypeList = await equipmentTypeServices.getEquipmentType(filterData);

  const warehouseList = await warehouseService.getWarehouses(filterData, '', '', -1);

  // Warehouse list properties
  let warehouseProperties = { ...properties };
  warehouseProperties.title = 'Warehouse';
  warehouseProperties.type = 'options';
  warehouseProperties.hint = 'Select';
  warehouseProperties.id = 'warehouse';
  warehouseProperties.fieldSortOrder = 0;
  warehouseProperties.iconUrl = `${imageUrl}/project.png`;
  warehouseProperties.options = warehouseList.map(item => {
    let warehouseOption = { ...option };
    warehouseOption.title = item?.name;
    warehouseOption.id = item?._id;
    return warehouseOption;
  });
  warehouseProperties.IsRequired = true;

  // Equipment row in warehouse field
  let equipmentRowInWarehouseProperties = { ...properties };
  equipmentRowInWarehouseProperties.title = 'Row in Warehouse';
  equipmentRowInWarehouseProperties.type = 'text';
  equipmentRowInWarehouseProperties.id = 'equipmentRow';
  equipmentRowInWarehouseProperties.fieldSortOrder = 0;
  equipmentRowInWarehouseProperties.iconUrl = `${imageUrl}/pencil-sharp.png`;
  equipmentRowInWarehouseProperties.hint = 'for example 6';
  equipmentRowInWarehouseProperties.IsRequired = true;

  // Equipment shelf in warehouse field
  let equipmentShelfInWarehouseProperties = { ...properties };
  equipmentShelfInWarehouseProperties.title = 'Shelf in Warehouse';
  equipmentShelfInWarehouseProperties.type = 'text';
  equipmentShelfInWarehouseProperties.id = 'equipmentShelf';
  equipmentShelfInWarehouseProperties.fieldSortOrder = 0;
  equipmentShelfInWarehouseProperties.iconUrl = `${imageUrl}/pencil-sharp.png`;
  equipmentShelfInWarehouseProperties.hint = 'for example 5';
  equipmentShelfInWarehouseProperties.IsRequired = true;

  // Quantity properties
  let quantityProperties = { ...properties };
  quantityProperties.title = 'Quantity';
  quantityProperties.type = 'number';
  quantityProperties.id = 'quantity';
  quantityProperties.iconUrl = `${imageUrl}/caret-forward-circle-sharp.png`;
  quantityProperties.IsRequired = true;
  quantityProperties.hint = 'Please enter quantity';
  quantityProperties.parentFieldId = 'equipmentType';
  quantityProperties.dependentIds = equipmentTypeList
    .filter(
      element => element.quantityType.quantityType == 'unique' && element.quantityType.isActive
    )
    .map(element => element._id);

  // QR Code Properties
  let qrCodeProperties = { ...properties };
  qrCodeProperties.title = 'QR Code Number';
  qrCodeProperties.type = 'qr-code';
  qrCodeProperties.id = 'qrCode';
  qrCodeProperties.fieldSortOrder = 0;
  qrCodeProperties.iconUrl = `${imageUrl}/pencil-sharp.png`;
  qrCodeProperties.hint = 'xxxxxxxxxxxxxxxxx';
  qrCodeProperties.IsRequired = false;

  let conditionProperties = { ...properties };
  conditionProperties.title = 'Condition';
  conditionProperties.type = 'options';
  conditionProperties.hint = 'Select';
  conditionProperties.id = 'condition';
  conditionProperties.fieldSortOrder = 0;
  conditionProperties.iconUrl = `${imageUrl}/project.png`;
  let conditionOpt = ['Ok', 'Quarantine', 'Write-off'];
  conditionProperties.options = conditionOpt.map(item => {
    let tempOption = { ...option };
    tempOption.title = item;
    tempOption.iconUrl = `${imageUrl}/caret-forward-circle-sharp.png`;
    tempOption.id = item.replace(/\s+/g, '_').toLowerCase();
    return tempOption;
  });
  conditionProperties.IsRequired = true;

  let subConditionProperties = { ...properties };
  subConditionProperties.title = 'Reason';
  subConditionProperties.type = 'options';
  subConditionProperties.hint = 'Select';
  subConditionProperties.id = 'reasonStatus';
  subConditionProperties.fieldSortOrder = 0;
  subConditionProperties.iconUrl = `${imageUrl}/project.png`;
  let subConditionOptions = [
    'repair-required',
    'certification-required',
    'damaged',
    'missing',
    'other',
  ];
  subConditionProperties.options = subConditionOptions.map(item => {
    let tempOption = { ...option };
    tempOption.title = item;
    tempOption.iconUrl = `${imageUrl}/caret-forward-circle-sharp.png`;
    tempOption.id = item.replace(/\s+/g, '_').toLowerCase();
    // Set visibility based on condition
    if (item === 'repair-required' || item === 'certification-required') {
      tempOption.isVisibleForOptions = ['quarantine'];
    } else if (item === 'damaged' || item === 'missing' || item === 'other') {
      tempOption.isVisibleForOptions = ['write-off'];
    }
    return tempOption;
  });
  subConditionProperties.IsRequired = false;
  subConditionProperties.isDefaultVisible = false;
  subConditionProperties.parentFieldId = 'condition';
  subConditionProperties.dependentIds = ['quarantine', 'write-off'];

  let otherReasonTextProperties = { ...properties };
  otherReasonTextProperties.title = 'Description';
  otherReasonTextProperties.type = 'text';
  otherReasonTextProperties.id = 'reason';
  otherReasonTextProperties.fieldSortOrder = 0;
  otherReasonTextProperties.iconUrl = `${imageUrl}/pencil-sharp.png`;
  otherReasonTextProperties.hint = 'Please Enter Description';
  otherReasonTextProperties.IsRequired = false;
  otherReasonTextProperties.dependentIds = ['other'];
  otherReasonTextProperties.IsRequired = false;
  otherReasonTextProperties.isDefaultVisible = false;
  otherReasonTextProperties.parentFieldId = 'reasonStatus';

  return [
    warehouseProperties,
    equipmentRowInWarehouseProperties,
    equipmentShelfInWarehouseProperties,
    quantityProperties,
    qrCodeProperties,
    conditionProperties,
    subConditionProperties,
    otherReasonTextProperties,
  ];
};

/**
 * check user role and get assign project list
 *
 * @param {*} filter
 * @param {*} user
 * @returns
 */
exports.getAndAddAssignProjectList = async (filter, user, type = '') => {
  try {
    if (![global.constant.ADMIN_ROLE, global.constant.SUPER_ADMIN_ROLE].includes(user.role)) {
      let searchData = {
        ...(user.roleId.isAssignAllProjects ? {} : { user: user.id }),
        account: user.account,
        deletedAt: null,
      };

      const projectList = await commonUtils.getAssignedProjectList(
        user.roleId.isAssignAllProjects,
        searchData
      );

      if (projectList.length > 0) {
        let filterKey = type === '' ? '_id' : 'project';

        filter[`${filterKey}`] = { $in: projectList };
      }
    }
  } catch (error) {
    console.error('Error fetching assigned projects:', error);
  }
  return filter;
};

const GetSetupReportStaticFile = async (user, status = true) => {
  let filterData = {
    account: user.account,
    ...(status && { isActive: true }),
    deletedAt: null,
    isDefault: false,
  };

  filterData = await this.getAndAddAssignProjectList(filterData, user);

  const projectList = await projectServices.getAllProjects({
    ...filterData,
    ...(this.projectStatus && { status: this.projectStatus }),
  });

  let commonFilter = {
    account: user.account,
    deletedAt: null,
  };
  commonFilter = await this.getAndAddAssignProjectList(commonFilter, user, 'location');
  const locationsList = await locationServices.getAllLocationByAccountId(commonFilter);
  const assetList = await assetServices.getAllAssetByAccountId(commonFilter);
  const reportList = await manageReportService.getReports({
    account: user.account,
    isPublish: true,
    deletedAt: null,
  });

  // Project properties
  let projectProperties = { ...properties };
  projectProperties.title = 'Project';
  projectProperties.type = 'options';
  projectProperties.hint = 'Select';
  projectProperties.id = 'project';
  // default project
  projectProperties.hasChildField = true;
  projectProperties.fieldSortOrder = 0;
  projectProperties.iconUrl = `${imageUrl}/ic_form_project.png`;
  projectProperties.options = projectList.map(item => {
    let projectOption = { ...option };
    projectOption.title =
      item?.projectNumber !== '' ? `${item.projectNumber} - ${item.title}` : item?.title;
    projectOption.id = item?._id;
    projectOption.isDefault =
      projectOption.title === process.env.DEFAULT_PROJECT.replace(/_/g, ' ');
    // default project
    projectOption.dependentFieldIds =
      projectOption.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ')
        ? ['defaultProject']
        : [];
    return projectOption;
  });
  projectProperties.IsRequired = true;

  // Asset Properties
  let assetProperties = { ...properties };
  assetProperties.title = 'Asset';
  assetProperties.type = 'options';
  assetProperties.hint = 'Select';
  assetProperties.id = 'cable';
  assetProperties.type = 'options';
  assetProperties.iconUrl = `${imageUrl}/ic_form_assest.png`;
  assetProperties.options = assetList.map(item => {
    let tempOption = { ...option };
    tempOption.title = item?.cableName;
    tempOption.isVisibleForOptions = [item?.fromLocation?._id, item?.toLocation?._id];
    tempOption.isDefault = tempOption.title == process.env.DEFAULT_ASSET.replace(/_/g, ' ');
    tempOption.id = item?._id;
    return tempOption;
  });
  assetProperties.IsRequired = false;
  assetProperties.parentFieldId = 'report';

  // Location properties
  let locationProperties = { ...properties };
  locationProperties.title = 'Location';
  locationProperties.type = 'options';
  locationProperties.hint = 'Select';
  locationProperties.id = 'location';
  locationProperties.fieldSortOrder = 0;
  locationProperties.iconUrl = `${imageUrl}/ic_form_location.png`;
  locationProperties.options = locationsList.map(item => {
    let tempOption = { ...option };
    tempOption.title = item.title;
    tempOption.isVisibleForOptions = item?.reports || [];
    tempOption.isVisibleForMultiAssetOptions = [];
    tempOption.id = item?._id;
    tempOption.isDefault = !!item?.isDefault;
    tempOption.dependentFieldIds =
      tempOption.title === process.env.DEFAULT_PROJECT.replace(/_/g, ' ') ? ['defaultProject'] : [];
    return tempOption;
  });
  locationProperties.parentFieldId = 'report';
  locationProperties.IsRequired = true;

  // manage report properties
  let manageReportProperties = { ...properties };
  manageReportProperties.title = 'Report';
  manageReportProperties.type = 'options';
  manageReportProperties.hint = 'Select';
  manageReportProperties.id = 'report';
  manageReportProperties.fieldSortOrder = 0;
  manageReportProperties.iconUrl = `${imageUrl}/ic_form_report.png`;
  manageReportProperties.options = reportList.map(item => {
    let tempOption = { ...option };
    tempOption.title = item.title;
    tempOption.isVisibleForOptions = item?.project?._id ? [item?.project?._id] : [];
    tempOption.id = item?._id;
    tempOption.type = item?.type;
    tempOption.status = item?.status;
    tempOption.isDefault = tempOption.title === process.env.DEFAULT_ASSET.replace(/_/g, ' ');
    tempOption.isProgressable = item?.isProgressable;
    tempOption.dependentFieldIds = [];
    return tempOption;
  });
  manageReportProperties.parentFieldId = 'project';
  manageReportProperties.IsRequired = true;

  // Multi Assets Properties
  let multiAssetProperties = { ...properties };
  multiAssetProperties.title = 'Asset';
  multiAssetProperties.type = 'multi-options';
  multiAssetProperties.hint = 'Select';
  multiAssetProperties.id = 'asset';
  multiAssetProperties.iconUrl = `${imageUrl}/ic_form_assest.png`;
  multiAssetProperties.options = assetList.map(item => {
    let tempOption = { ...option };
    tempOption.title = item?.cableName;
    tempOption.isVisibleForOptions = item?.reports || [];
    tempOption.id = item?._id;
    return tempOption;
  });
  multiAssetProperties.IsRequired = false;
  multiAssetProperties.parentFieldId = 'report';

  assetList.forEach(element => {
    locationProperties.options.forEach(item => {
      if (
        [element.fromLocation._id.toString(), element.toLocation._id.toString()].includes(
          item.id.toString()
        )
      ) {
        item.isVisibleForMultiAssetOptions = Array.from(
          // eslint-disable-next-line no-undef
          new Set([...element.reports, ...item.isVisibleForOptions])
        );
      }
    });
  });

  let ReportFields = [
    projectProperties,
    manageReportProperties,
    locationProperties,
    assetProperties,
    multiAssetProperties,
  ];

  return ReportFields;
};

const GetSetupToolboxTalkStaticFile = async (user, status = true) => {
  let filterData = {
    account: user.account,
    ...(status && { isActive: true }),
    deletedAt: null,
    isDefault: false,
  };
  const filter = {
    account: user.account,
    ...(status && { isActive: true }),
    deletedAt: null,
  };
  filterData = await this.getAndAddAssignProjectList(filterData, user);

  const projectList = await projectServices.getAllProjects({
    ...filterData,
    ...(this.projectStatus && { status: this.projectStatus }),
  });

  let commonFilter = {
    account: user.account,
    deletedAt: null,
  };
  commonFilter = await this.getAndAddAssignProjectList(commonFilter, user, 'location');

  const locationsList = await locationServices.getAllLocationByAccountId(commonFilter);
  const teamsList = await teamServices.getAllTeam(filter);
  const memberList = await memberServices.getAllAccountMember(user.account);

  // Project properties
  let projectProperties = { ...properties };
  projectProperties.title = 'Project';
  projectProperties.type = 'options';
  projectProperties.hint = 'Select project';
  projectProperties.id = 'project';
  // default project
  projectProperties.hasChildField = true;
  projectProperties.fieldSortOrder = 0;
  projectProperties.iconUrl = `${imageUrl}/project.png`;
  projectProperties.options = projectList.map(item => {
    let projectOption = { ...option };
    projectOption.title =
      item?.projectNumber !== '' ? `${item.projectNumber} - ${item.title}` : item?.title;
    projectOption.id = item?._id;
    projectOption.isDefault =
      projectOption.title === process.env.DEFAULT_PROJECT.replace(/_/g, ' ');
    // default project
    projectOption.dependentFieldIds =
      projectOption.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ')
        ? ['defaultProject']
        : [];
    return projectOption;
  });
  projectProperties.IsRequired = true;

  // Location properties
  let locationProperties = { ...properties };
  locationProperties.title = 'Location';
  locationProperties.type = 'options';
  locationProperties.hint = 'Select location';
  locationProperties.id = 'location';
  locationProperties.fieldSortOrder = 0;
  locationProperties.iconUrl = `${imageUrl}/ic_form_location.png`;
  locationProperties.options = locationsList.map(item => {
    let tempOption = { ...option };
    tempOption.title = item.title;
    tempOption.isVisibleForOptions = [item?.project?._id];
    tempOption.id = item?._id;
    tempOption.isDefault = !!item?.isDefault;
    tempOption.dependentFieldIds =
      tempOption.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ') ? ['defaultProject'] : [];
    return tempOption;
  });
  locationProperties.parentFieldId = 'project';
  locationProperties.IsRequired = true;

  // Team properties
  let teamProperties = { ...properties };
  teamProperties.title = 'Team';
  teamProperties.type = 'options';
  teamProperties.hint = 'Select Team';
  teamProperties.id = 'team';
  teamProperties.fieldSortOrder = 0;
  teamProperties.iconUrl = `${imageUrl}/ic_form_team.png`;
  teamProperties.options = teamsList.map(item => {
    let tempOption = { ...option };
    tempOption.title = item.teamsWfmName;
    tempOption.isVisibleForOptions = [item?.project?._id];
    tempOption.id = item?._id;
    tempOption.isDefault = !!item?.isDefault;
    tempOption.dependentFieldIds =
      tempOption.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ') ? ['defaultProject'] : [];
    return tempOption;
  });
  teamProperties.parentFieldId = 'project';
  teamProperties.IsRequired = true;

  // Note field
  let noteProperties = { ...properties };
  noteProperties.title = 'Note';
  noteProperties.type = 'textarea';
  noteProperties.fieldSortOrder = 0;
  noteProperties.id = 'note';
  noteProperties.iconUrl = `${imageUrl}/pencil-sharp.png`;
  noteProperties.hint = 'Write note here';
  noteProperties.IsRequired = true;
  noteProperties.dependentFieldIds =
    noteProperties.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ')
      ? ['defaultProject']
      : [];

  // Photo field
  let photoProperties = { ...properties };
  photoProperties.title = 'Photo';
  photoProperties.type = 'photo';
  photoProperties.fieldSortOrder = 0;
  photoProperties.id = 'photo';
  photoProperties.iconUrl = `${imageUrl}/pencil-sharp.png`;
  photoProperties.selectedOption = [];
  photoProperties.dependentFieldIds =
    photoProperties.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ') ? ['otherProject'] : [];
  photoProperties.IsRequired = true;

  // Button field
  let buttonProperties = { ...properties };
  buttonProperties.title = 'Save';
  buttonProperties.type = 'button';
  buttonProperties.fieldSortOrder = 0;
  buttonProperties.id = 'button';
  buttonProperties.iconUrl = `${imageUrl}/pencil-sharp.png`;
  buttonProperties.selectedOption = [];
  buttonProperties.parentFieldId = 'photo';

  // Member Properties
  let memberProperties = { ...properties };
  memberProperties.title = 'Member';
  memberProperties.type = 'options';
  memberProperties.hint = 'Select';
  memberProperties.id = 'member';
  memberProperties.type = 'multi-options';
  memberProperties.iconUrl = `${imageUrl}/ic_form_host.png`;
  memberProperties.options = memberList.map(item => {
    let teamOption = { ...option };

    let newName;
    if (
      item?.user?.callingName !== undefined &&
      item?.user?.callingName !== null &&
      item?.user?.callingName !== ''
    ) {
      newName = item?.user?.callingName.trim();
    } else if (
      item?.user?.firstName !== undefined &&
      item?.user?.firstName !== null &&
      item?.user?.firstName !== ''
    ) {
      newName = item?.user?.firstName; // default value
    } else {
      newName = 'N/A'; // default value
    }
    teamOption.title = newName + ' ' + item?.user?.lastName;
    teamOption.isVisibleForOptions = [item?.project?._id];
    teamOption.id = item?.user._id;
    teamOption.dependentFieldIds =
      teamOption.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ') ? ['defaultProject'] : [];
    return teamOption;
  });

  memberProperties.IsRequired = true;
  memberProperties.parentFieldId = 'project';

  // Logged in User Properties
  let userProperties = { ...properties };
  userProperties.title = 'Host';
  userProperties.type = 'options';
  userProperties.hint = 'Select';
  userProperties.id = 'user';
  userProperties.type = 'options';
  userProperties.IsRequired = true;
  userProperties.iconUrl = `${imageUrl}/ic_form_host.png`;
  userProperties.parentFieldId = 'project';
  userProperties.options = memberList.map(member => {
    let tempOption = { ...option };

    let newName;
    if (
      member?.user?.callingName !== undefined &&
      member?.user?.callingName !== null &&
      member?.user?.callingName !== ''
    ) {
      newName = member?.user?.callingName.trim();
    } else if (
      member?.user?.firstName !== undefined &&
      member?.user?.firstName !== null &&
      member?.user?.firstName !== ''
    ) {
      newName = member?.user?.firstName; // default value
    } else {
      newName = 'N/A'; // default value
    }
    tempOption.title = `${newName} ${member?.user?.lastName}`;
    tempOption.isVisibleForOptions = [member?.project?._id];
    tempOption.id = member?.user._id;
    tempOption.dependentFieldIds =
      tempOption.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ') ? ['defaultProject'] : [];
    return tempOption;
  });

  // Signature Properties
  let signatureProperties = { ...properties };
  signatureProperties.title = 'Please draw a signature in below PAD';
  signatureProperties.type = 'signature';
  signatureProperties.fieldSortOrder = 0;
  signatureProperties.id = 'signature';
  signatureProperties.iconUrl = `${imageUrl}/pencil-sharp.png`;
  signatureProperties.hint = 'Please sign here...';
  signatureProperties.dependentFieldIds =
    signatureProperties.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ')
      ? ['defaultProject']
      : [];

  return [
    projectProperties,
    locationProperties,
    teamProperties,
    noteProperties,
    photoProperties,
    buttonProperties,
    memberProperties,
    userProperties,
    signatureProperties,
  ];
};

module.exports = {
  GetSafeCardStaticFile,
  GetUnSafeCardStaticFile,
  GetNCRCardStaticFile,
  GetIncidentCardStaticFile,
  getShiftStaticFile,
  getReportStaticFile,
  getShiftActivityStaticFile,
  getFeedbackStaticFile,
  GetEquipmentStaticFile,
  GetImageSpecificationAndPriceStaticFile,
  GetWarehouseInfoFields,
  GetSetupReportStaticFile,
  GetSetupToolboxTalkStaticFile,
  constructFunction,
};
