const mongoose = require('mongoose');

// Services
const pmOrderService = require('../services/pm-order.service');
const pmOrderManageEquipmentService = require('../services/pm-order-manage-equipment.service');
const equipmentOrderService = require('../services/equipment-order.service');
const equipmentService = require('../services/equipment.service');
const equipmentOrderHistoryService = require('../services/equipment-order-history.service');
const inventoryHistoryService = require('../services/inventory-history.service');
const shoppingCartService = require('../services/shopping-cart.service');

// Utils
const constantUtils = require('../utils/constants.utils');
const responseUtils = require('../utils/response.utils');
const commonUtils = require('../utils/common.utils');
const { validateSearch } = require('../utils/common-function.utils');
const HTTP_STATUS = require('../utils/status-codes');

/**
 * Create PM Order Request
 *
 * @param {*} req
 * @param {*} res
 */
exports.addToQueuePMOrder = async (req, res) => {
  try {
    const { project, equipmentType } = req.body;

    /*-- Create or Update PM Order --*/
    const { account, _id: userId } = req.userData;

    // Create or Update PM Order
    const pmOrderId = await this.createOrUpdatePMOrder(account, project, userId);

    // Create or Update PM Order Manage Equipment
    await this.createOrUpdatePMOrderManageEquipment(account, pmOrderId, equipmentType, userId);

    res.status(200).json(responseUtils.successResponse(constantUtils.ADD_EQ_ORDER_IN_QUEUE));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

exports.pmOrderRequest = async (req, res) => {
  try {
    const { pmOrderId } = req.params;
    const reqData = req.body;
    let randString = commonUtils.generateOrderNumber(10, 'order');

    const exists = await pmOrderService.getPMOrderById(pmOrderId);
    if (!exists) {
      return res
        .status(400)
        .json(responseUtils.errorResponse(constantUtils.PM_ORDER_REQUEST_NOT_FOUND));
    }

    let updateData = {
      orderNumber: randString,
      fromDate: reqData.fromDate,
      ...(reqData.comments ? { comments: reqData.comments } : {}),
      toDate: reqData.toDate,
      status: 'requested',
      updatedBy: req.userData._id,
      updatedAt: new Date(),
    };

    await pmOrderService.updatePMOrder(pmOrderId, updateData);

    if ('pmOrderManageData' in reqData) {
      for (const element of reqData.pmOrderManageData) {
        await pmOrderManageEquipmentService.updatePMOrderManageEquipmentByPMOrderId(
          { pmOrder: pmOrderId, status: 'open' },
          {
            status: 'requested',
            fromPeriod: element.fromPeriod,
            toPeriod: element.toPeriod,
            updatedBy: req.userData._id,
            updatedAt: new Date(),
          }
        );
      }
    }

    await equipmentOrderService.updateEquipmentOrderByFilter(
      { pmOrderId, status: 'queue' },
      { status: 'requested', updatedBy: req.userData._id }
    );

    if ('pmOrderManageData' in reqData) {
      await this.updatePMOrderManageEquipmentDetails(reqData.pmOrderManageData);
    }

    res.status(200).json(responseUtils.successResponse(constantUtils.CREATE_PM_ORDER_REQUEST));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

exports.getPMOrders = async (req, res) => {
  try {
    let page = req.query.page ?? null;
    let perPage = req.query.perPage ?? null;
    let search = await validateSearch(req.query.search);

    if (!search && search !== '') {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.INVALID_SEARCH));
    }
    let filterData = {
      account: req.userData.account,
      deletedAt: null,
      ...(req.query.status ? { status: req.query.status } : { status: { $ne: 'open' } }),
    };

    // Add project filter if project status is provided
    if ('projectStatus' in req.query) {
      filterData = await commonUtils.filterProjectStatus(
        req.query.projectStatus,
        req.userData.account,
        filterData
      );
    }

    delete filterData?.projectStatus;

    const pmOrders = await pmOrderService.getPMOrderList(filterData, page, perPage, search);

    return res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.GET_PM_ORDER_REQUEST, pmOrders));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

exports.updatePMOrderManageEquipment = async (req, res) => {
  try {
    const { pmOrderManageId } = req.params;
    const reqData = req.body;
    reqData.updatedBy = req.userData._id;
    reqData.updatedAt = new Date();

    const exists = await pmOrderManageEquipmentService.searchPMOrderManageEquipment({
      _id: pmOrderManageId,
      account: req.userData.account,
      deletedAt: null,
    });
    if (!exists) {
      return res
        .status(400)
        .json(responseUtils.errorResponse(constantUtils.PM_ORDER_DETAILS_NOT_FOUND));
    }

    const pmOrderManageData = await pmOrderManageEquipmentService.updatePMOrderManageEquipment(
      pmOrderManageId,
      reqData
    );

    res
      .status(200)
      .json(
        responseUtils.successResponse(constantUtils.UPDATE_PM_ORDER_DETAILS, pmOrderManageData)
      );
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Scan Equipment
 *
 * @param {*} req
 * @param {*} res
 */
exports.scanEquipment = async (req, res) => {
  try {
    let { orderId, qrCode } = req.query;
    const [equipmentData] = await equipmentService.findQRCode(qrCode);

    if (!equipmentData) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.EQUIPMENT_NOT_FOUND));
    }

    let filter = {
      pmOrder: commonUtils.toObjectId(orderId),
      equipment: { $in: equipmentData._id },
      account: req.userData.account,
      deletedAt: null,
    };

    let response = await pmOrderManageEquipmentService.searchPMOrderManageEquipment(filter);

    if (!response) {
      return res
        .status(400)
        .json(responseUtils.errorResponse(constantUtils.EQUIPMENT_NOT_LINKED_IN_ORDER));
    }

    let equipmentHistory = await equipmentOrderHistoryService.getEquipmentOrderHistoryOneByFilter({
      pmOrder: commonUtils.toObjectId(orderId),
      equipment: equipmentData._id,
      account: req.userData.account,
      deletedAt: null,
    });

    let equipment = {
      _id: equipmentData._id,
      pmManageOrderId: response._id,
      name: equipmentData.name,
      image: equipmentData.equipmentImage,
      equipmentType: equipmentData.equipmentType,
      pmRequestedQuantity: response.pmRequestedQuantity,
      wmDispatchQuantity: equipmentHistory.wmDispatchQuantity,
      wmApprovedQuantity: response.wmApprovedQuantity,
      pmReceivedQuantity: equipmentHistory.pmReceivedQuantity,
      pmDispatchQuantity: equipmentHistory.pmDispatchQuantity,
      requestedDate: response.createdAt,
      comments: {
        wmComments: response.wmComments,
        pmComments: response.pmComments,
      },
    };

    res.status(200).json(responseUtils.successResponse(constantUtils.GET_EQUIPMENT, equipment));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Scan Equipment
 *
 * @param {*} req
 * @param {*} res
 */
exports.manageInventory = async (req, res) => {
  const session = await mongoose.startSession();
  const transactionOptions = {
    readConcern: { level: 'snapshot' },
    writeConcern: { w: 'majority' },
    readPreference: 'primary',
  };

  try {
    session.startTransaction(transactionOptions);

    let {
      status,
      pmReceivedQuantity = null,
      pmDispatchQuantity = null,
      wmReceivedQuantity = null,
      subStatus,
      checkinReasonStatus,
      checkinReason,
      orderId,
      equipment,
      equipmentType,
    } = req.body;

    let response = await pmOrderManageEquipmentService.searchPMOrderManageEquipment({
      pmOrder: commonUtils.toObjectId(orderId),
      equipment: { $in: equipment },
      equipmentType: commonUtils.toObjectId(equipmentType),
      account: req.userData.account,
      deletedAt: null,
    });

    if (!response) {
      await session.abortTransaction();
      return res
        .status(400)
        .json(responseUtils.errorResponse(constantUtils.LINKED_EQUIPMENT_NOT_FOUND));
    }

    let data = {
      ...(pmReceivedQuantity !== null && { pmReceivedQuantity }),
      ...(pmDispatchQuantity !== null && { pmDispatchQuantity }),
      ...(wmReceivedQuantity !== null && { wmReceivedQuantity }),
      ...(checkinReasonStatus !== null && { checkinReasonStatus }),
      ...(checkinReason !== null && { checkinReason }),
    };

    let updatePMOrderManageEquipment =
      await pmOrderManageEquipmentService.updatePMOrderManageEquipmentByPMOrderId(
        {
          pmOrder: commonUtils.toObjectId(orderId),
          equipmentType: commonUtils.toObjectId(equipmentType),
          account: req.userData.account,
          deletedAt: null,
        },
        data,
        session
      );

    if (!updatePMOrderManageEquipment) {
      await session.abortTransaction();
      return res
        .status(400)
        .json(responseUtils.errorResponse(constantUtils.LINKED_EQUIPMENT_NOT_FOUND));
    }

    /**
     * BUSINESS LOGIC: Synchronize PM Check-in Reason Data for Web Portal Access
     *
     * WHY THIS CODE WAS ADDED:
     * - Mobile PM users can set quarantine/write-off reasons when managing project inventory
     * - This data was previously only stored in pm-order-manage-equipment table
     * - Web portal needs direct access to current equipment status and reasons
     * - This sync ensures equipment model has the latest reason data for web display
     *
     * FLOW:
     * 1. Mobile PM manages project inventory and sets status (quarantine/write-off) + reason
     * 2. Data gets stored in pm-order-manage-equipment (existing mobile flow - line 270-295)
     * 3. This sync code ALSO updates the equipment model directly
     * 4. Web portal can now read reason directly from equipment without complex joins
     *
     * DATA MAPPING:
     * - checkinReasonStatus (pm-order-manage-equipment) → reasonStatus (equipment)
     * - checkinReason (pm-order-manage-equipment) → reason (equipment)
     * - subStatus (pm-order-manage-equipment) → condition (equipment)
     *
     * EXAMPLE MOBILE REQUEST:
     * POST /api/pm-order/manage-inventory
     * { "subStatus": "write-off", "checkinReasonStatus": "other", "checkinReason": "its a op product you know" }
     *
     * NOTE: PM orders can have multiple equipment, so we update all equipment in the array
     */
    if (checkinReasonStatus || checkinReason || subStatus) {
      const equipmentArray = Array.isArray(equipment) ? equipment : [equipment];

      // Update all equipment in the array
      for (const equipmentId of equipmentArray) {
        let equipmentUpdateData = {
          updatedBy: req.userData._id,
          updatedAt: new Date(),
        };

        // Set reason fields if provided
        if (checkinReasonStatus) {
          equipmentUpdateData.reasonStatus = checkinReasonStatus;
        }
        if (checkinReason) {
          equipmentUpdateData.reason = checkinReason;
        }

        // Set condition based on subStatus
        if (subStatus === 'quarantine') {
          equipmentUpdateData.condition = 'quarantine';
        } else if (subStatus === 'write-off') {
          equipmentUpdateData.condition = 'write-off';
        }

        await equipmentService.updateEquipment(equipmentId, equipmentUpdateData, session);
      }
    }

    let filter = {
      pmOrder: commonUtils.toObjectId(orderId),
      equipment: commonUtils.toObjectId(equipment),
      account: req.userData.account,
      deletedAt: null,
    };

    if (status === 'pre-check-in') {
      filter.status = 'in-transit';
    } else if (status === 'check-out') {
      filter.status = 'check-in';
    } else if (status === 'in-stock') {
      filter.status = 'check-out';
    } else {
      filter.status = null;
    }

    let updateData = {
      status,
      ...(pmReceivedQuantity !== null && { pmReceivedQuantity }),
      ...(pmDispatchQuantity !== null && { pmDispatchQuantity }),
      ...(wmReceivedQuantity !== null && { wmReceivedQuantity }),
      subStatus,
      updatedBy: req.userData._id,
      updatedAt: new Date(),
    };

    const updateEquip = await this.updateEquipmentOrderHistory(filter, updateData, session, res);

    if (!updateEquip) {
      await session.abortTransaction();
      return res
        .status(400)
        .json(responseUtils.errorResponse(constantUtils.LINKED_EQUIPMENT_NOT_FOUND));
    }

    await this.prepareInventoryHistory(
      equipment,
      status,
      orderId,
      pmReceivedQuantity,
      req,
      session
    );

    await session.commitTransaction();

    res.status(200).json(responseUtils.successResponse(`Equipment added to ${status}`));
  } catch (error) {
    await session.abortTransaction();
    res.status(500).json(responseUtils.errorResponse(error.message));
  } finally {
    session.endSession();
  }
};

/**
 * Get Order Status Count
 *
 * @param {*} req
 * @param {*} res
 */
exports.getOrderStatusCount = async (req, res) => {
  try {
    let filter = {
      account: req.userData.account,
      deletedAt: null,
    };
    const response = await pmOrderService.getOrderStatusCountList(filter);
    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.GET_ORDER_STATUS_COUNT, response));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Get Equipment Order History
 *
 * @param {*} req
 * @param {*} res
 */
exports.getEquipmentOrderHistory = async (req, res) => {
  try {
    let filter = {
      account: req.userData.account,
      equipment: commonUtils.toObjectId(req.params.equipment),
      deletedAt: null,
    };

    let page = parseInt(req.query.page) || 0;
    let perPage = parseInt(req.query.perPage) || 10;

    const response = await pmOrderService.getEquipmentOrderHistoryByFilter(filter, page, perPage);

    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.GET_EQUIPMENT_ORDER_HISTORY, response));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

exports.createOrUpdatePMOrder = async (account, project, userId) => {
  let filterPMOrder = {
    account,
    project,
    status: 'open',
  };

  const searchPMOrder = await pmOrderService.searchPMOrder(filterPMOrder);

  let pmOrderId = searchPMOrder ? searchPMOrder._id : '';
  if (!searchPMOrder) {
    filterPMOrder.createdBy = userId;
    const createPMOrder = await pmOrderService.createPMOrder(filterPMOrder);
    pmOrderId = createPMOrder._id;
  } else {
    filterPMOrder.updatedBy = userId;
    await pmOrderService.updatePMOrder(pmOrderId, filterPMOrder);
  }
  return pmOrderId;
};

exports.createOrUpdatePMOrderManageEquipment = async (
  account,
  pmOrderId,
  equipmentType,
  userId,
  shoppingCart
) => {
  for (let key in equipmentType) {
    let prepareEquipmentData = {
      pmOrder: pmOrderId,
      account,
      equipmentType: equipmentType[key].typeId,
      status: 'open',
    };

    const searchPMOrderManageEquipment =
      await pmOrderManageEquipmentService.searchPMOrderManageEquipment(prepareEquipmentData);
    let pmOrderManageEquipmentId = searchPMOrderManageEquipment
      ? searchPMOrderManageEquipment._id
      : '';
    prepareEquipmentData = {
      ...prepareEquipmentData,
      pmRequestedQuantity: equipmentType[key].pmRequestedQuantity,
      fromPeriod: equipmentType[key].fromPeriod ?? null,
      toPeriod: equipmentType[key].toPeriod ?? null,
    };

    if (!searchPMOrderManageEquipment) {
      prepareEquipmentData.pmComments = equipmentType[key].pmComments ?? [];
      prepareEquipmentData.createdBy = userId;
      const createPMOrderManageEquipment =
        await pmOrderManageEquipmentService.createPMOrderManageEquipment(prepareEquipmentData);
      pmOrderManageEquipmentId = createPMOrderManageEquipment._id;
    } else {
      prepareEquipmentData.updatedBy = userId;
      prepareEquipmentData.pmRequestedQuantity =
        parseInt(searchPMOrderManageEquipment.pmRequestedQuantity) +
        parseInt(equipmentType[key].pmRequestedQuantity);
      if (equipmentType[key]?.pmComments && searchPMOrderManageEquipment.pmComments.length > 0) {
        const compareArrays = (arr1, arr2) => JSON.stringify(arr1) === JSON.stringify(arr2);
        if (
          !compareArrays(searchPMOrderManageEquipment.pmComments, equipmentType[key].pmComments)
        ) {
          prepareEquipmentData.pmComments = searchPMOrderManageEquipment.pmComments.concat(
            equipmentType[key].pmComments
          );
        }
      } else {
        prepareEquipmentData.pmComments = equipmentType[key].pmComments ?? [];
      }
      await pmOrderManageEquipmentService.updatePMOrderManageEquipment(
        pmOrderManageEquipmentId,
        prepareEquipmentData
      );
    }

    // Update Equipment Order
    await this.updateEquipmentOrders(
      equipmentType[key].users,
      pmOrderId,
      pmOrderManageEquipmentId,
      userId,
      shoppingCart
    );
  }
};

exports.updateEquipmentOrders = async (
  users,
  pmOrderId,
  pmOrderManageEquipmentId,
  userId,
  shoppingCart
) => {
  for (let userKey in users) {
    let prepareRequestData = {
      pmApprovedQuantity: users[userKey].pmApprovedQuantity,
      pmOrderId,
      pmOrderManageEquipment: pmOrderManageEquipmentId,
      status: 'queue',
      shoppingCart,
      updatedBy: userId,
      updatedAt: new Date(),
    };
    await pmOrderService.updateEquipmentOrder(users[userKey].requestId, prepareRequestData);
  }
};

exports.updatePMOrderManageEquipmentDetails = async pmOrderManageData => {
  for (let key in pmOrderManageData) {
    let manageId = pmOrderManageData[key].id;
    delete pmOrderManageData[key].id;
    let updateData = {};
    for (let subKey in pmOrderManageData[key]) {
      if (pmOrderManageData[key][subKey] !== null) {
        updateData[subKey] = pmOrderManageData[key][subKey];
      }
    }
    await pmOrderManageEquipmentService.updatePMOrderManageEquipment(manageId, updateData);
  }
};

exports.prepareInventoryHistory = async (
  equipment,
  status,
  orderId,
  pmReceivedQuantity,
  req,
  session
) => {
  if (status === 'check-in') {
    const getPMOrder = await pmOrderService.getPMOrderById(commonUtils.toObjectId(orderId));
    await inventoryHistoryService.prepareInventoryHistoryAndCreate(
      commonUtils.toObjectId(equipment),
      'on-site',
      'order-received',
      getPMOrder.project.title,
      getPMOrder.orderNumber,
      pmReceivedQuantity,
      '',
      commonUtils.toObjectId(orderId),
      req.userData.account,
      req.userData._id,
      session
    );
  }
};

exports.updateEquipmentOrderHistory = async (filter, updateData, session) => {
  const response = await equipmentOrderHistoryService.updateEquipmentHistory(
    filter,
    updateData,
    session
  );
  if (!response) {
    return false;
  }
  return true;
};

/**
 * Get Linked Equipment List
 *
 * @param {*} req
 * @param {*} res
 */
exports.getLinkedEquipmentList = async (req, res) => {
  try {
    const { manageId } = req.params;
    let filter = {
      pmOrderManageEquipment: manageId,
      status: 'pre-check-in',
      account: req.userData.account,
      deletedAt: null,
    };

    const response = await equipmentOrderHistoryService.getEquipmentOrderHistoryByFilter(filter);

    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.GET_LINKED_EQUIPMENT, response));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Scan Linked Equipment in PM Check In
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.scanLinkedEquipmentInPMCheckIn = async (req, res) => {
  try {
    const { manageId, qrCode } = req.params;

    const [getEquipment] = await equipmentService.findQRCode(qrCode);

    if (!getEquipment) {
      return res
        .status(400)
        .json(responseUtils.errorResponse(constantUtils.SCANNED_QR_NOT_IN_ORDER));
    }

    const [response] = await equipmentOrderHistoryService.getEquipmentOrderHistoryByFilter({
      pmOrderManageEquipment: manageId,
      equipment: getEquipment._id,
      status: 'pre-check-in',
      account: req.userData.account,
      deletedAt: null,
    });

    if (!response) {
      return res
        .status(400)
        .json(responseUtils.errorResponse(constantUtils.SCANNED_EQUIPMENT_NOT_LINKED));
    }

    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.GET_LINKED_EQUIPMENT, response));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Update Linked Equipment In PM Check-in
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.updateLinkedCheckInOrder = async (req, res) => {
  try {
    const { historyId } = req.params;
    const { quantity } = req.body;

    const exist = await equipmentOrderHistoryService.getEquipmentOrderHistory(historyId);

    if (!exist) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.ORDER_NOT_FOUND));
    }

    if (quantity > exist.pmReceivedQuantity) {
      return res
        .status(400)
        .json(responseUtils.errorResponse(constantUtils.INVALID_REQUESTED_QUANTITY));
    }

    let pmReceivedQuantity = exist.pmReceivedQuantity - quantity;
    let status = pmReceivedQuantity > 0 ? exist.status : 'in-transit';

    const response = await equipmentOrderHistoryService.updateEquipmentOrderHistory(historyId, {
      pmReceivedQuantity,
      status,
      updatedBy: req.userData._id,
      updatedAt: new Date(),
    });

    res
      .status(200)
      .json(
        responseUtils.successResponse(constantUtils.LINKED_EQUIPMENT_UPDATED_SUCCESSFULLY, response)
      );
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Update Request Quantity From Shopping Cart
 *
 * @param {*} req
 * @param {*} res
 */
exports.updateRequestQuantityFromShoppingCart = async (req, res) => {
  try {
    let { pmManageOrderId } = req.params;

    let calculateQuantity = 0;
    for (let reqData of req.body) {
      let { equipmentRequestId, requestedQuantity } = reqData;
      await equipmentOrderService.updateEquipmentOrder(equipmentRequestId, {
        pmApprovedQuantity: requestedQuantity,
        updatedBy: req.userData._id,
        updatedAt: new Date(),
      });
      calculateQuantity += requestedQuantity;
    }

    const manageEquipment = await pmOrderManageEquipmentService.updatePMOrderManageEquipments(
      pmManageOrderId,
      { pmRequestedQuantity: calculateQuantity, updatedBy: req.userData._id, updatedAt: new Date() }
    );

    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.UPDATE_PM_ORDER_DETAILS, manageEquipment));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * get pm orders projects
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */

exports.getPMOrdersProjects = async (req, res) => {
  try {
    let page = req.query.page ?? null;
    let perPage = req.query.perPage ?? null;
    let sort = req.query.sort && req.query.sort === 'asc' ? 1 : -1;
    let project = req.query.project ? commonUtils.toObjectId(req.query.project) : null;
    let search = req.query.search ? await validateSearch(req.query.search) : null;

    let filterData = {
      account: req.userData.account,
      deletedAt: null,
      ...(req.query.status ? { status: req.query.status } : { status: { $ne: 'open' } }),
      ...(project !== null && { project }),
    };

    // Add project filter if project status is provided
    if ('projectStatus' in req.query) {
      filterData = await commonUtils.filterProjectStatus(
        req.query.projectStatus,
        req.userData.account,
        filterData,
        project
      );
    }

    delete filterData?.projectStatus;

    const pmOrders = await pmOrderService.getPMOrdersProjects(
      filterData,
      page,
      perPage,
      sort,
      search
    );

    return res
      .status(HTTP_STATUS.OK)
      .json(responseUtils.successResponse(constantUtils.GET_PM_ORDER_REQUEST, pmOrders));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * get PM order list
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.getPMOrdersList = async (req, res) => {
  try {
    let page = req.query.page ?? null;
    let perPage = req.query.perPage ?? null;
    let search = await validateSearch(req.query.search);

    if (!search && search !== '') {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(responseUtils.errorResponse(constantUtils.INVALID_SEARCH));
    }

    let filterData = {
      project: commonUtils.toObjectId(req.params.id),
      account: req.userData.account,
      deletedAt: null,
      ...(req.query.status ? { status: req.query.status } : { status: { $ne: 'open' } }),
    };

    const pmOrders = await pmOrderService.getPMOrderList(filterData, page, perPage, search);

    return res
      .status(HTTP_STATUS.OK)
      .json(responseUtils.successResponse(constantUtils.GET_PM_ORDER_REQUEST, pmOrders));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Create PM Order Request
 *
 * @param {*} req
 * @param {*} res
 */
exports.addToQueuePMOrderV2 = async (req, res) => {
  try {
    const { project, shoppingCart, equipmentType } = req.body;

    /*-- Create or Update PM Order --*/
    const { account, _id: userId } = req.userData;

    // Create or Update PM Order
    const pmOrderId = await this.createOrUpdateShoppingCartPMOrder(
      account,
      project,
      userId,
      shoppingCart
    );

    // Create or Update PM Order Manage Equipment
    await this.createOrUpdateShoppingCartPMOrderManageEquipment(
      account,
      pmOrderId,
      equipmentType,
      userId,
      shoppingCart
    );

    await equipmentOrderService.updateEquipmentOrderByFilter(
      { shoppingCart, status: { $ne: 'rejected' } },
      { status: 'queue' }
    );

    res
      .status(HTTP_STATUS.OK)
      .json(responseUtils.successResponse(constantUtils.ADD_EQ_ORDER_IN_QUEUE));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Create or update shopping cart pm order
 *
 * @param {*} account
 * @param {*} project
 * @param {*} userId
 * @returns
 */
exports.createOrUpdateShoppingCartPMOrder = async (account, project, userId, shoppingCart) => {
  let filterPMOrder = {
    account,
    project,
    status: 'open',
    shoppingCart,
  };

  const searchPMOrder = await pmOrderService.searchPMOrder(filterPMOrder);

  let pmOrderId = searchPMOrder ? searchPMOrder._id : '';
  if (!searchPMOrder) {
    filterPMOrder.createdBy = userId;
    const createPMOrder = await pmOrderService.createPMOrder(filterPMOrder);
    pmOrderId = createPMOrder._id;
  } else {
    filterPMOrder.updatedBy = userId;
    await pmOrderService.updatePMOrder(pmOrderId, filterPMOrder);
  }
  return pmOrderId;
};

/**
 * Create or update shopping cart pm order manage equipment
 *
 * @param {*} account
 * @param {*} pmOrderId
 * @param {*} equipmentType
 * @param {*} userId
 * @param {*} shoppingCart
 */
exports.createOrUpdateShoppingCartPMOrderManageEquipment = async (
  account,
  pmOrderId,
  equipmentType,
  userId,
  shoppingCart
) => {
  for (let key in equipmentType) {
    let prepareEquipmentData = {
      pmOrder: pmOrderId,
      account,
      equipmentType: equipmentType[key].typeId,
      status: 'open',
    };

    const searchPMOrderManageEquipment =
      await pmOrderManageEquipmentService.searchPMOrderManageEquipment(prepareEquipmentData);
    let pmOrderManageEquipmentId = searchPMOrderManageEquipment
      ? searchPMOrderManageEquipment._id
      : '';
    prepareEquipmentData = {
      ...prepareEquipmentData,
      pmRequestedQuantity: equipmentType[key].pmRequestedQuantity,
      fromPeriod: equipmentType[key].fromPeriod ?? null,
      toPeriod: equipmentType[key].toPeriod ?? null,
    };

    if (!searchPMOrderManageEquipment) {
      prepareEquipmentData.pmComments = equipmentType[key].pmComments ?? [];
      prepareEquipmentData.createdBy = userId;
      const createPMOrderManageEquipment =
        await pmOrderManageEquipmentService.createPMOrderManageEquipment(prepareEquipmentData);
      pmOrderManageEquipmentId = createPMOrderManageEquipment._id;
    } else {
      prepareEquipmentData.updatedBy = userId;
      prepareEquipmentData.pmRequestedQuantity =
        parseInt(searchPMOrderManageEquipment.pmRequestedQuantity) +
        parseInt(equipmentType[key].pmRequestedQuantity);
      if (equipmentType[key]?.pmComments && searchPMOrderManageEquipment.pmComments.length > 0) {
        const compareArrays = (arr1, arr2) => JSON.stringify(arr1) === JSON.stringify(arr2);
        if (
          !compareArrays(searchPMOrderManageEquipment.pmComments, equipmentType[key].pmComments)
        ) {
          prepareEquipmentData.pmComments = searchPMOrderManageEquipment.pmComments.concat(
            equipmentType[key].pmComments
          );
        }
      } else {
        prepareEquipmentData.pmComments = equipmentType[key].pmComments ?? [];
      }
      await pmOrderManageEquipmentService.updatePMOrderManageEquipment(
        pmOrderManageEquipmentId,
        prepareEquipmentData
      );
    }

    // Update Equipment Order
    await this.updateEquipmentOrders(
      equipmentType[key].users,
      pmOrderId,
      pmOrderManageEquipmentId,
      userId,
      shoppingCart
    );
  }
};

/* ******************** Version 2 Methods **********************  */

/**
 * Update PM Order Request V2
 *
 * @param {*} req
 * @param {*} res
 */
exports.pmOrderRequestV2 = async (req, res) => {
  try {
    const { pmOrderId } = req.params;
    const reqData = req.body;
    let randString = commonUtils.generateOrderNumber(10, 'order');

    const exists = await pmOrderService.getPMOrderById(pmOrderId);
    if (!exists) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(responseUtils.errorResponse(constantUtils.PM_ORDER_REQUEST_NOT_FOUND));
    }

    let updateData = {
      orderNumber: randString,
      fromDate: reqData.fromDate ?? null,
      ...(reqData.comments ? { comments: reqData.comments } : {}),
      toDate: reqData.toDate ?? null,
      status: 'requested',
      shoppingCart: reqData.shoppingCart ?? null,
      createdBy: req.userData._id,
      updatedBy: req.userData._id,
      updatedAt: new Date(),
    };

    // Update PM Order
    await pmOrderService.updatePMOrder(pmOrderId, updateData);

    if ('pmOrderManageData' in reqData) {
      for (const element of reqData.pmOrderManageData) {
        await pmOrderManageEquipmentService.updatePMOrderManageEquipmentByPMOrderId(
          { pmOrder: pmOrderId, status: 'open' },
          {
            status: 'requested',
            fromPeriod: element.fromPeriod ?? null,
            toPeriod: element.toPeriod ?? null,
            createdBy: req.userData._id,
            updatedBy: req.userData._id,
            updatedAt: new Date(),
          }
        );
      }
    }

    // Update Equipment Order
    await equipmentOrderService.updateEquipmentOrderByFilter(
      { pmOrderId, status: 'queue' },
      { status: 'requested', updatedBy: req.userData._id }
    );

    // Update PM Order Manage Equipment Details
    if ('pmOrderManageData' in reqData) {
      await this.updatePMOrderManageEquipmentDetails(reqData.pmOrderManageData);
    }

    // Update Shopping Cart
    if ('shoppingCart' in reqData) {
      await shoppingCartService.updateShoppingCartById(reqData.shoppingCart, {
        status: 'requested',
        updatedBy: req.userData._id,
        updatedAt: new Date(),
      });
    }

    res
      .status(HTTP_STATUS.OK)
      .json(responseUtils.successResponse(constantUtils.CREATE_PM_ORDER_REQUEST));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(responseUtils.errorResponse(error.message));
  }
};
