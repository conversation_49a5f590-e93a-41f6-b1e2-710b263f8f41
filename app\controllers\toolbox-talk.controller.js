const mongoose = require('mongoose');

// Services
const toolboxTalkService = require('../services/toolbox-talk.service');
const pdfTemplateService = require('../services/pdf-template.service');

// Utils
const constantUtils = require('../utils/constants.utils');
const { successResponse, errorResponse } = require('../utils/response.utils');
const responseUtils = require('../utils/response.utils');
const commonUtils = require('../utils/common.utils');
const { toObjectId } = require('../utils/common.utils');
const commonFunctionsUtils = require('../utils/common-function.utils');

/**
 * Create Toolbox Talk
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.createToolboxTalk = async (req, res) => {
  try {
    let reqData = req.body;
    reqData.account = req.userData.account;
    reqData.createdBy = req.userData._id;
    const createResponse = await toolboxTalkService.createToolboxTalk(reqData);

    // update sync api manage data
    if (createResponse) {
      await this.commonUpdateSyncApiManage(req.userData.account);
    }
    return res.status(200).json(successResponse(constantUtils.CREATE_TOOLBOX_TALK, createResponse));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

exports.getToolboxTalks = async (req, res) => {
  try {
    let { project = null, location = null, team = null, projectStatus } = req.query;
    let page = req.query.page ? req.query.page : 0;
    let perPage = req.query.perPage ? req.query.perPage : 10;
    let created =
      req.query?.created && req.query?.created !== 'all'
        ? await commonUtils.getCreatedDateFilter({
            created: req.query.created,
            fromDate: req.query.fromDate,
            toDate: req.query.toDate,
          })
        : null;

    let filter = {
      account: req.userData.account,
      ...(project &&
        mongoose.Types.ObjectId.isValid(project) && {
          project: toObjectId(project),
        }),
      ...(location &&
        mongoose.Types.ObjectId.isValid(location) && {
          location: toObjectId(location),
        }),
      ...(team && mongoose.Types.ObjectId.isValid(team) && { team: toObjectId(team) }),
      ...(created !== null && {
        createdAt: created.createdAt,
      }),
      deletedAt: null,
    };

    // Add project filter if project status is provided
    if (projectStatus && project === 'all') {
      filter = await commonUtils.filterProjectStatus(
        projectStatus,
        req.userData.account,
        filter,
        req?.assignedProjectList
      );
    }

    const getResponse = await toolboxTalkService.getToolboxTalks(filter, 'list', page, perPage);
    // add all records count
    let finalResponse = {
      toolboxTalkData: getResponse,
      currentPage: Number(page),
    };
    finalResponse = await commonUtils.getCountFromQuery('toolbox-talk', filter, finalResponse);
    return res.status(200).json(successResponse(constantUtils.GET_TOOLBOX_TALKS, finalResponse));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

exports.getToolboxTalkById = async (req, res) => {
  try {
    const id = req.params.id;
    let filter = {
      _id: toObjectId(id),
      account: req.userData.account,
      deletedAt: null,
    };
    const getResponse = await toolboxTalkService.getToolboxTalks(filter);
    let finalResponse = getResponse.length > 0 ? getResponse[0] : {};
    return res.status(200).json(successResponse(constantUtils.GET_TOOLBOX_TALKS, finalResponse));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};
/**
 * Delete Toolbox Talk
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.deleteToolboxTalk = async (req, res) => {
  try {
    const id = toObjectId(req.params.id);
    const deleteResponse = await toolboxTalkService.deleteToolboxTalk(id, req.deletedAt);

    // update sync api manage data
    if (deleteResponse) {
      await this.commonUpdateSyncApiManage(req.userData.account);
    }
    return res.status(200).json(successResponse(constantUtils.DELETE_TOOLBOX_TALK, deleteResponse));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Get Toolbox Talk PDF Details
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.getToolboxTalkPDFDetails = async (req, res) => {
  try {
    const { toolboxId } = req.params;

    const filter = {
      _id: toObjectId(toolboxId),
      account: req.userData.account,
      deletedAt: null,
    };

    const [response] = await toolboxTalkService.getToolboxTalkPDFDetails(filter);
    if (response.length === 0) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.MISSING_TOOLBOX_DATA));
    }

    return await pdfTemplateService.exportToolboxTalkPDF(response, res);
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

exports.commonUpdateSyncApiManage = async account => {
  await commonFunctionsUtils.updateSyncApiManage({
    syncApis: ['projectDocuments'],
    account,
  });
};
